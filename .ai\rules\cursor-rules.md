# StudyScore.info Cursor AI Rules

## Code Style and Structure
- Write concise, technical TypeScript code with accurate examples
- Use functional and declarative programming patterns; avoid classes
- Prefer iteration and modularization over code duplication
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError, canAccess)
- Structure files: exported component, subcomponents, helpers, static content, types
- Organize components by domain: `features/` for business logic, `ui/` for reusable components

## Naming Conventions
- Use lowercase with dashes for directories (e.g., `components/research-card`, `features/quality-scoring`)
- Favor named exports for components
- Use PascalCase for component files (e.g., `ResearchCard.tsx`, `QualityBadge.tsx`)
- Prefix custom hooks with `use` (e.g., `useResearchSearch`, `useQualityFilter`)

## TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types
- Avoid enums; use const objects or union types instead
- Use functional components with TypeScript interfaces
- Define strict types for research data, quality scores, and API responses
- Use generic types for reusable components (e.g., `Card<T>`, `DataTable<T>`)

## Syntax and Formatting
- Use the "function" keyword for pure functions
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements
- Use declarative JSX with semantic HTML for accessibility
- Follow Biome formatting rules (configured in project)
- Use template literals for dynamic strings

## Error Handling and Validation
- Prioritize error handling: handle errors and edge cases early
- Use early returns and guard clauses
- Implement proper error logging and user-friendly messages
- Use Zod for form validation and API response validation
- Model expected errors as return values in Server Actions
- Use error boundaries for unexpected errors
- Handle research data loading states gracefully
- Validate quality scores and research metadata

## UI and Styling
- Use Shadcn UI, Radix, and Tailwind CSS for components and styling
- Implement responsive design with Tailwind CSS; use a mobile-first approach
- Follow StudyScore design system:
  - Primary color: `text-primary` (blue #2563eb)
  - Quality indicators: Green for Tier 1, Blue for Tier 2
  - Academic typography with proper hierarchy
- Use semantic HTML for research content (articles, citations, etc.)
- Implement proper ARIA labels for accessibility

## Performance Optimization
- Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC)
- Wrap client components in Suspense with fallback
- Use dynamic loading for non-critical components
- Optimize images: use WebP format, include size data, implement lazy loading
- Implement virtual scrolling for large research result sets
- Use React.memo for expensive research card renders
- Optimize search and filtering with debouncing

## StudyScore-Specific Conventions
- Use consistent quality scoring display (Tier 1: 90-100, Tier 2: 75-89)
- Implement proper citation formatting (APA, MLA, Vancouver)
- Handle research paper metadata consistently
- Use semantic versioning for API changes
- Follow academic data standards for research content

## Key Conventions
- Use 'nuqs' for URL search parameter state management
- Optimize Web Vitals (LCP, CLS, FID)
- Limit 'use client':
  - Favor server components and Next.js SSR
  - Use only for Web API access in small components
  - Avoid for data fetching or state management
- Use Server Actions for form submissions and data mutations
- Implement proper loading states for research data

## Data Fetching and State Management
- Use Server Components for initial data loading
- Implement proper caching strategies for research data
- Use React Query/TanStack Query for client-side data management (when needed)
- Handle pagination and infinite scrolling efficiently
- Implement proper search debouncing and caching

## Security and Privacy
- Validate all user inputs, especially search queries
- Implement proper rate limiting for API calls
- Handle sensitive research data appropriately
- Use environment variables for API keys and secrets
- Implement proper CORS policies

## Testing and Quality Assurance
- Write unit tests for utility functions and hooks
- Test components with realistic research data
- Implement integration tests for search and filtering
- Use TypeScript strict mode for better type safety
- Run Biome linting and formatting before commits

## Documentation
- Document complex research algorithms and quality scoring logic
- Use JSDoc comments for public APIs
- Maintain README files for major features
- Document API endpoints and data schemas

## Follow Next.js 15 Best Practices
- Use App Router for all routing
- Implement proper metadata for SEO
- Use Server Actions for form handling
- Follow Next.js docs for Data Fetching, Rendering, and Routing
- Implement proper error pages (404, 500)
- Use Next.js Image component for optimization
