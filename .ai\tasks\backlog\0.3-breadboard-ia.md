---
phase: 0.3
title: "Breadboard Site Information Architecture"
classification: AGENT_READY
complexity: Low
dependencies: [0.2]
---

# Breadboard Site Information Architecture

## Overview

Define all routes and page affordances in a concise IA document so UI stubs match your end goals.

## Acceptance Criteria

- [ ] `docs/breadboard.md` created
- [ ] All major routes listed with their components/links
- [ ] External vs. internal navigation clearly marked

## Implementation Guide

1. **Create** `docs/breadboard.md`

2. **List routes** (home, blog, projects, whiteboards, footer)

3. **Under each**, annotate which UI pieces (cards, tables, embeds) will appear

4. **Mark** whether links are external (`target="_blank"`) or internal

### Example Structure

```md
# Information Architecture

- `/`  
  - **ActivityCard** (static stub → will display weekly running goal)  
  - **SocialLinks** (external LinkTree style)  
  - **ProjectLinks** (cards linking out)  

- `/blog`  
  - **BlogIndex** (list of articles)  

- `/blog/[slug]`  
  - **BlogPost**  
    - Content (from MDX or markdown)  
    - Table of Contents (TOC)  
    - View counter (to implement)  

- `/whiteboards`  
  - **WhiteboardCanvas** (Embed TLDraw / Excalidraw)  

- **Footer**  
  - Internal links: About, Blog, Whiteboards  
  - External links: socials, "Built with…"  
```

## Test Requirements

1. **Documentation Test**:
   - Verify `docs/breadboard.md` exists and is properly formatted
   - Confirm all major routes are documented
   - Check that component responsibilities are clearly defined

## Definition of Done

- [ ] Information architecture document created
- [ ] All planned routes documented
- [ ] Component responsibilities defined
- [ ] Navigation structure clarified
- [ ] External vs internal links identified

## Branch Name

`feat/0.3-breadboard-ia`

## Notes for AI Agent

- This is a planning phase focused on documentation
- The IA document will guide subsequent UI implementation
- Consider the StudyScore context when defining routes and components
