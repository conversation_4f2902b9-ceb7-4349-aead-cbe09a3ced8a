---
phase: 0.4
title: "Scaffold Core Tooling & Codebase Layout"
classification: AGENT_READY
complexity: Medium
dependencies: [0.3]
---

# Scaffold Core Tooling & Codebase Layout

## Overview

Install & configure TypeScript, Next.js, Tailwind, Biome, VS Code settings—and establish your `src/` folder skeleton.

## Acceptance Criteria

- [ ] `tsconfig.json`, `next.config.mjs`, `tailwind.config.ts`, `postcss.config.mjs`, `biome.json` created per spec
- [ ] `.vscode/settings.json` & `extensions.json` added
- [ ] Folder structure under `src/` matches the spec:

  ```
  src/
    ├── app/
    ├── components/
    ├── lib/
    ├── hooks/
    └── types/
  ```
- [ ] `npm run dev`, `npm run lint`, `npm run typecheck` all pass

## Implementation Guide

1. **Copy** configuration files from phase 0.1 into project root.

2. **Run**

   ```bash
   npm install
   npm run typecheck
   npm run lint
   ```

3. **Add** VS Code workspace config under `.vscode/`

### VS Code Configuration

#### `.vscode/settings.json`

```json
{
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.defaultFormatter": "biomejs.biome",
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  }
}
```

#### `.vscode/extensions.json`

```json
{
  "recommendations": [
    "biomejs.biome",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode"
  ]
}
```

## Test Requirements

1. **Configuration Test**:
   - Run `npm run typecheck` to ensure TypeScript is properly configured
   - Run `npm run lint` to verify Biome linting works
   - Run `npm run format` to test code formatting

2. **Development Server Test**:
   - Run `npm run dev` and verify the server starts without errors

## Definition of Done

- [ ] All configuration files created and working
- [ ] VS Code workspace properly configured
- [ ] Folder structure matches specification
- [ ] All npm scripts execute successfully
- [ ] Development server runs without errors

## Branch Name

`feat/0.4-scaffold-tooling`

## Notes for AI Agent

- This phase focuses on tooling setup and configuration
- Ensure all configuration files match the Phase 0.1 specifications
- VS Code settings should enhance the development experience
