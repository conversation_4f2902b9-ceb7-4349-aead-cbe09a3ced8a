---
phase: 0.5
title: "Initial UI Stub & Layout"
classification: AGENT_READY
complexity: Low
dependencies: [0.4]
---

# Initial UI Stub & Layout

## Overview

Implement your global layout, basic navbar/footer, and the homepage stub from V0ero in real React+TSX.

## Acceptance Criteria

- [ ] `src/app/layout.tsx` with `<html>`, `<body>` and global CSS import
- [ ] `src/app/page.tsx` renders stub welcome screen
- [ ] Stub components for SocialLinks, ProjectCards, ActivityCard created under `components/`
- [ ] Tailwind styles applied (container, spacing, colors)

## Implementation Guide

1. **Create** `layout.tsx` and `page.tsx` per phase 0.1 examples.

2. **Under** `src/components/ui/` stub out:

   * `SocialLinks.tsx`
   * `ProjectLinks.tsx`
   * `ActivityCard.tsx`

3. **Import** and place them in `page.tsx`.

4. **Verify** `npm run dev` shows stub UI without errors.

### Component Templates

#### `src/components/ui/SocialLinks.tsx`

```tsx
export function SocialLinks() {
  return (
    <div className="flex gap-4">
      <a href="#" className="text-muted-foreground hover:text-foreground">
        GitHub
      </a>
      <a href="#" className="text-muted-foreground hover:text-foreground">
        Twitter
      </a>
      <a href="#" className="text-muted-foreground hover:text-foreground">
        LinkedIn
      </a>
    </div>
  );
}
```

#### `src/components/ui/ProjectLinks.tsx`

```tsx
export function ProjectLinks() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="p-4 border rounded-lg">
        <h3 className="font-semibold mb-2">Project 1</h3>
        <p className="text-muted-foreground">Description of project 1</p>
      </div>
      <div className="p-4 border rounded-lg">
        <h3 className="font-semibold mb-2">Project 2</h3>
        <p className="text-muted-foreground">Description of project 2</p>
      </div>
    </div>
  );
}
```

#### `src/components/ui/ActivityCard.tsx`

```tsx
export function ActivityCard() {
  return (
    <div className="p-4 border rounded-lg">
      <h3 className="font-semibold mb-2">Weekly Activity</h3>
      <p className="text-muted-foreground">Activity data will be displayed here</p>
    </div>
  );
}
```

## Test Requirements

1. **Component Test**:
   - Verify all stub components render without errors
   - Check that Tailwind styles are applied correctly

2. **Layout Test**:
   - Confirm global layout renders properly
   - Verify CSS imports work correctly

3. **Development Server Test**:
   - Run `npm run dev` and verify the homepage displays all components

## Definition of Done

- [ ] Global layout implemented
- [ ] Homepage with stub components created
- [ ] All components render without errors
- [ ] Tailwind styles properly applied
- [ ] Development server shows complete UI

## Branch Name

`feat/0.5-initial-ui-stub`

## Notes for AI Agent

- Focus on creating functional stub components
- Ensure proper TypeScript typing for all components
- Components should be ready for real data integration in later phases
