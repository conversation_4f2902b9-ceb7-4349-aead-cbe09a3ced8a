---
phase: 0.8
title: "Add Blog & Whiteboard Integrations"
classification: AGENT_READY
complexity: Medium
dependencies: [0.7]
---

# Add Blog & Whiteboard Integrations

## Overview

Implement the blog index/[slug] pages via MDX, plus a whiteboard embed using Excalidraw or TLDraw.

## Acceptance Criteria

- [ ] `src/app/blog/page.tsx` lists MDX posts from `/posts/`
- [ ] `src/app/blog/[slug]/page.tsx` renders MDX content + TOC + view counter
- [ ] `/whiteboards` route embeds Excalidraw iframe
- [ ] Fallback UI for when embed script fails

## Implementation Guide

1. **Install** `@next/mdx` and configure in `next.config.mjs`

2. **Place** `.mdx` files under `posts/`, each with front-matter (title, date)

3. **Implement** file-system loader in `lib/mdx.ts`

4. **Embed** Excalidraw via `<iframe src="https://excalidraw.com/...">`.

### Dependencies

```bash
npm install @next/mdx @mdx-js/loader @mdx-js/react @types/mdx
```

### Next.js Configuration

Update `next.config.mjs`:

```javascript
import createMDX from '@next/mdx';

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: [],
    formats: ['image/avif', 'image/webp'],
  },
  pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],
};

const withMDX = createMDX({
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

export default withMDX(nextConfig);
```

### MDX Utilities

#### `src/lib/mdx.ts`

```typescript
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

const postsDirectory = path.join(process.cwd(), 'posts');

export interface BlogPost {
  slug: string;
  title: string;
  date: string;
  excerpt?: string;
  content: string;
}

export function getAllPosts(): BlogPost[] {
  const fileNames = fs.readdirSync(postsDirectory);
  const allPostsData = fileNames
    .filter((name) => name.endsWith('.mdx'))
    .map((fileName) => {
      const slug = fileName.replace(/\.mdx$/, '');
      const fullPath = path.join(postsDirectory, fileName);
      const fileContents = fs.readFileSync(fullPath, 'utf8');
      const { data, content } = matter(fileContents);

      return {
        slug,
        content,
        title: data.title,
        date: data.date,
        excerpt: data.excerpt,
      };
    });

  return allPostsData.sort((a, b) => (a.date < b.date ? 1 : -1));
}

export function getPostBySlug(slug: string): BlogPost | null {
  try {
    const fullPath = path.join(postsDirectory, `${slug}.mdx`);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);

    return {
      slug,
      content,
      title: data.title,
      date: data.date,
      excerpt: data.excerpt,
    };
  } catch {
    return null;
  }
}
```

### Blog Pages

#### `src/app/blog/page.tsx`

```tsx
import Link from 'next/link';
import { getAllPosts } from '@/lib/mdx';

export default function BlogIndex() {
  const posts = getAllPosts();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Blog</h1>
      <div className="space-y-6">
        {posts.map((post) => (
          <article key={post.slug} className="border-b pb-6">
            <Link href={`/blog/${post.slug}`}>
              <h2 className="text-xl font-semibold hover:text-brand-blue">
                {post.title}
              </h2>
            </Link>
            <p className="text-muted-foreground text-sm mt-2">{post.date}</p>
            {post.excerpt && (
              <p className="mt-2 text-muted-foreground">{post.excerpt}</p>
            )}
          </article>
        ))}
      </div>
    </div>
  );
}
```

#### `src/app/blog/[slug]/page.tsx`

```tsx
import { notFound } from 'next/navigation';
import { MDXRemote } from 'next-mdx-remote/rsc';
import { getPostBySlug, getAllPosts } from '@/lib/mdx';

interface Props {
  params: { slug: string };
}

export async function generateStaticParams() {
  const posts = getAllPosts();
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

export default function BlogPost({ params }: Props) {
  const post = getPostBySlug(params.slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <article className="prose prose-lg max-w-none">
        <h1>{post.title}</h1>
        <p className="text-muted-foreground">{post.date}</p>
        <MDXRemote source={post.content} />
      </article>
    </div>
  );
}
```

### Whiteboard Integration

#### `src/app/whiteboards/page.tsx`

```tsx
'use client';

import { useState } from 'react';

export default function Whiteboards() {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Whiteboards</h1>
      
      {hasError ? (
        <div className="border rounded-lg p-8 text-center">
          <p className="text-muted-foreground">
            Failed to load whiteboard. Please try again later.
          </p>
        </div>
      ) : (
        <div className="relative w-full h-[600px] border rounded-lg overflow-hidden">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <p className="text-muted-foreground">Loading whiteboard...</p>
            </div>
          )}
          <iframe
            src="https://excalidraw.com/"
            className="w-full h-full"
            onLoad={() => setIsLoading(false)}
            onError={() => {
              setIsLoading(false);
              setHasError(true);
            }}
          />
        </div>
      )}
    </div>
  );
}
```

## Test Requirements

1. **Blog Test**:
   - Create sample MDX files in `/posts/`
   - Verify blog index lists all posts
   - Test individual blog post rendering

2. **Whiteboard Test**:
   - Verify Excalidraw iframe loads correctly
   - Test error handling for failed embeds

## Definition of Done

- [ ] MDX configuration working
- [ ] Blog index and post pages functional
- [ ] Whiteboard embed working with error handling
- [ ] Sample blog posts created for testing
- [ ] All routes accessible and functional

## Branch Name

`feat/0.8-blog-whiteboard-integrations`

## Notes for AI Agent

- Install required MDX dependencies using npm
- Create sample blog posts for testing
- Ensure proper error handling for external embeds
