---
phase: "1.1"
classification: "agent-ready"
complexity: "high"
estimated_hours: 8
dependencies: []
assignee: "devin-ai"
created: 2024-01-15
priority: "CRITICAL"
---

# 🤖 Phase 1.1: Deploy AML Pipeline as REST Endpoint

## Agent Assignment: READY
**Estimated Complexity:** High (8 hours)
**Dependencies:** None (uses existing pipeline YAML)
**Priority:** CRITICAL - Foundation for all backend functionality

## Background Context
Transform the existing `docstream_pipeline_v2_deployment.yml` from a batch job submission model to a managed online endpoint. This enables real-time REST API calls to process individual PDFs, making the pipeline accessible as a service rather than requiring job submission workflows.

## Acceptance Criteria
- [ ] Managed online endpoint deployed from existing pipeline YAML
- [ ] REST API accepts PDF blob URI and processing parameters
- [ ] Endpoint returns job status and result locations
- [ ] Auto-scaling configuration (min 1, max 10 instances)
- [ ] Authentication using managed identity or API keys
- [ ] Monitoring and logging enabled
- [ ] Health check endpoint implemented
- [ ] SLA monitoring (95% availability, <15min response time)

## Implementation Guide
**Code patterns to follow:** `.ai/rules/azure-ml-endpoint-standards.md`
**Reference implementations:** `.ai/references/aml-online-endpoint-patterns.md`
**Dependencies:** Azure ML SDK v2, existing pipeline components
**File locations:** 
- `deployment/aml_endpoint_config.yml`
- `deployment/endpoint_deployment.py`
- `src/endpoint/scoring_script.py`
- `tests/test_endpoint_integration.py`

## Test Requirements
- Integration test with deployed endpoint
- Load testing (concurrent requests)
- Authentication and authorization tests
- Error handling tests (invalid inputs, timeouts)
- Health check endpoint validation
- Auto-scaling behavior tests

## Definition of Done
- [ ] Endpoint deployed and accessible via REST API
- [ ] All tests passing (unit, integration, load)
- [ ] Monitoring dashboards configured
- [ ] Documentation updated with API specifications
- [ ] Performance benchmarks met
- [ ] Security review completed

## Technical Specifications
```yaml
# Endpoint Configuration
name: docstream-pipeline-endpoint
compute: Standard_E4ds_v4
instance_count: 1
max_instances: 10
request_timeout_ms: 900000  # 15 minutes
max_concurrent_requests_per_instance: 1
```

**API Contract:**
```json
POST /score
{
    "blob_uri": "https://storage.../incoming-pdfs/doc.pdf",
    "document_id": "uuid4",
    "processing_config": {
        "enable_abcde_extraction": true,
        "enable_causal_prescoring": true,
        "chunk_size": 500,
        "chunk_overlap": 100,
        "enable_section_filtering": true
    }
}

Response:
{
    "job_id": "uuid4",
    "status": "running|completed|failed",
    "result_locations": {
        "markdown": "https://storage.../results/doc.md",
        "embeddings": "https://storage.../results/doc_embeddings.jsonl",
        "metadata": "https://storage.../results/doc_meta.json"
    },
    "processing_time_seconds": 450,
    "error_message": null
}
```

**Environment Variables:**
- `AZURE_ML_WORKSPACE_NAME`
- `AZURE_ML_RESOURCE_GROUP`
- `AZURE_ML_SUBSCRIPTION_ID`
- `STORAGE_CONNECTION_STRING`
- `ACS_CONNECTION_STRING`

## Notes for AI Agent
- Use the existing `docstream_pipeline_v2_deployment.yml` as the base
- Modify the pipeline to accept REST input instead of job parameters
- Implement proper error handling and timeout management
- Use managed identity for secure access to storage and ACS
- Configure auto-scaling based on queue depth and response time
- Include comprehensive logging for debugging and monitoring
- Test with the exact same components used in the existing pipeline
- Ensure backward compatibility with existing pipeline outputs

## Pipeline Adaptation Required
- Modify input handling to accept REST parameters
- Update output handling to return structured JSON response
- Add health check logic to the scoring script
- Implement proper exception handling and error responses
- Add request correlation IDs for tracing

## Branch Name
`feat/1.1-deploy-aml-endpoint`
