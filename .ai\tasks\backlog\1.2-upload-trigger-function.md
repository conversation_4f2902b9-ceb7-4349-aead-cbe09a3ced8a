---
phase: "1.2"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 6
dependencies: ["1.1"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 1.2: Upload Trigger Function

## Agent Assignment: READY
**Estimated Complexity:** Medium (6 hours)
**Dependencies:** AML Pipeline Endpoint (1.1)
**Priority:** HIGH - Enables end-to-end upload workflow

## Background Context
Create an Azure Function that triggers when PDFs are uploaded to blob storage, then calls the deployed AML pipeline endpoint. This function bridges the gap between user uploads and document processing, handling the orchestration of the processing workflow.

## Acceptance Criteria
- [ ] Azure Function triggered by blob storage events
- [ ] Function calls AML pipeline endpoint with uploaded PDF
- [ ] Proper error handling and retry logic
- [ ] Status tracking in Azure Table Storage
- [ ] Logging and monitoring integration
- [ ] Support for batch uploads
- [ ] Dead letter queue for failed processing
- [ ] Webhook notifications for completion

## Implementation Guide
**Code patterns to follow:** `.ai/rules/azure-functions-standards.md`
**Reference implementations:** `.ai/references/blob-trigger-patterns.md`
**Dependencies:** Azure Functions SDK, Azure Storage SDK, requests
**File locations:** 
- `functions/upload_trigger/__init__.py`
- `functions/upload_trigger/function.json`
- `functions/shared/pipeline_client.py`
- `functions/shared/status_tracker.py`

## Test Requirements
- Unit tests for function logic
- Integration tests with blob storage
- Integration tests with AML endpoint
- Error handling tests (network failures, invalid files)
- Performance tests (concurrent uploads)
- Monitoring and alerting validation

## Definition of Done
- [ ] Function deployed and responding to blob events
- [ ] All tests passing (unit, integration, performance)
- [ ] Error handling working correctly
- [ ] Status tracking operational
- [ ] Monitoring dashboards configured
- [ ] Documentation updated

## Technical Specifications
```python
# Function Configuration
{
    "scriptFile": "__init__.py",
    "bindings": [
        {
            "name": "myblob",
            "type": "blobTrigger",
            "direction": "in",
            "path": "incoming-pdfs/{name}",
            "connection": "AzureWebJobsStorage"
        }
    ]
}
```

**Function Logic:**
```python
import azure.functions as func
import requests
import json
from shared.pipeline_client import PipelineClient
from shared.status_tracker import StatusTracker

def main(myblob: func.InputStream):
    # Extract document metadata
    document_id = extract_document_id(myblob.name)
    blob_uri = get_blob_uri(myblob.name)
    
    # Update status to processing
    status_tracker = StatusTracker()
    status_tracker.update_status(document_id, "processing")
    
    # Call AML pipeline endpoint
    pipeline_client = PipelineClient()
    result = pipeline_client.process_document(blob_uri, document_id)
    
    # Update final status
    if result.success:
        status_tracker.update_status(document_id, "completed", result.locations)
    else:
        status_tracker.update_status(document_id, "failed", error=result.error)
```

**Environment Variables:**
- `AML_ENDPOINT_URL`
- `AML_ENDPOINT_KEY`
- `STORAGE_CONNECTION_STRING`
- `TABLE_STORAGE_CONNECTION_STRING`

## Notes for AI Agent
- Use blob trigger binding for automatic activation
- Implement exponential backoff for AML endpoint calls
- Store processing status in Azure Table Storage
- Include comprehensive error logging
- Handle large file uploads gracefully
- Implement dead letter queue for failed processing
- Add correlation IDs for request tracing
- Consider using Service Bus for better reliability

## Error Handling Strategy
- Retry failed AML endpoint calls (max 3 attempts)
- Log all errors with correlation IDs
- Move failed items to dead letter queue
- Send notifications for persistent failures
- Implement circuit breaker pattern for endpoint calls

## Branch Name
`feat/1.2-upload-trigger-function`
