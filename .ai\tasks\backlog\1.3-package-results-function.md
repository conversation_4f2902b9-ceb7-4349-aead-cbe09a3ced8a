---
phase: "1.3"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 5
dependencies: ["1.2"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 1.3: Package and Link Results Function

## Agent Assignment: READY
**Estimated Complexity:** Medium (5 hours)
**Dependencies:** Upload Trigger Function (1.2)
**Priority:** HIGH - Completes processing pipeline

## Background Context
After the AML endpoint completes processing, this function packages all output files (markdown, embeddings, metadata, causal report) into a single downloadable ZIP file and generates secure download links. This provides users with a complete results package for their processed documents.

## Acceptance Criteria
- [ ] Collect all pipeline output files from storage
- [ ] Package files into organized ZIP structure
- [ ] Generate secure SAS download links
- [ ] Update status with download information
- [ ] Set appropriate expiration times
- [ ] Include processing summary metadata
- [ ] Handle missing or corrupted output files
- [ ] Clean up temporary files

## Implementation Guide
**Code patterns to follow:** `.ai/rules/azure-functions-standards.md`
**Reference implementations:** `.ai/references/file-packaging-patterns.md`
**Dependencies:** Azure Functions SDK, Azure Storage SDK, zipfile
**File locations:** 
- `functions/package_results/__init__.py`
- `functions/package_results/function.json`
- `functions/shared/file_packager.py`
- `functions/shared/sas_generator.py`

## Test Requirements
- Unit tests for packaging logic
- Integration tests with storage
- Error handling tests (missing files, corrupted data)
- Performance tests (large file handling)
- Security tests (SAS link validation)
- Cleanup verification tests

## Definition of Done
- [ ] Function packages results correctly
- [ ] All tests passing (unit, integration, security)
- [ ] SAS links working with proper expiration
- [ ] Error handling operational
- [ ] Cleanup processes working
- [ ] Documentation updated

## Technical Specifications
```python
# Function Configuration
{
    "scriptFile": "__init__.py",
    "bindings": [
        {
            "name": "req",
            "type": "httpTrigger",
            "direction": "in",
            "methods": ["post"]
        },
        {
            "name": "$return",
            "type": "http",
            "direction": "out"
        }
    ]
}
```

**ZIP Package Structure:**
```
document_id_results.zip
├── README.md                    # Processing summary
├── document.md                  # Extracted markdown
├── embeddings.jsonl            # Vector embeddings
├── metadata.json               # Document metadata
├── causal_report.txt           # AI-generated causal analysis
├── quality_scores.json         # Quality assessment
└── processing_log.txt          # Processing details
```

**Function Logic:**
```python
import azure.functions as func
import zipfile
import tempfile
from shared.file_packager import FilePackager
from shared.sas_generator import SASGenerator

def main(req: func.HttpRequest) -> func.HttpResponse:
    document_id = req.params.get('document_id')
    
    # Collect all output files
    packager = FilePackager()
    files = packager.collect_output_files(document_id)
    
    # Create ZIP package
    zip_path = packager.create_zip_package(document_id, files)
    
    # Upload to results container
    blob_name = f"results/{document_id}_results.zip"
    packager.upload_package(zip_path, blob_name)
    
    # Generate SAS download link
    sas_generator = SASGenerator()
    download_url = sas_generator.generate_download_link(
        blob_name, 
        expiry_hours=72
    )
    
    # Update status with download info
    status_tracker.update_status(document_id, "completed", {
        "download_url": download_url,
        "expires_at": expiry_time,
        "file_size": file_size
    })
    
    return func.HttpResponse(json.dumps({
        "download_url": download_url,
        "expires_at": expiry_time,
        "file_size": file_size
    }))
```

**Environment Variables:**
- `STORAGE_CONNECTION_STRING`
- `RESULTS_CONTAINER_NAME`
- `SAS_EXPIRY_HOURS=72`
- `MAX_PACKAGE_SIZE_MB=100`

## Notes for AI Agent
- Implement robust file collection with retries
- Handle partial processing results gracefully
- Include comprehensive README in each package
- Use streaming for large file operations
- Implement proper cleanup of temporary files
- Add virus scanning for downloaded packages
- Include processing metrics in package
- Validate all files before packaging

## Package Contents
- **README.md**: Processing summary, file descriptions, usage instructions
- **document.md**: Clean markdown extraction from PDF
- **embeddings.jsonl**: Vector embeddings for semantic search
- **metadata.json**: Document metadata, quality scores, processing info
- **causal_report.txt**: AI-generated causality analysis
- **quality_scores.json**: Detailed quality assessment breakdown
- **processing_log.txt**: Technical processing details and timing

## Security Considerations
- Generate time-limited SAS tokens (72 hours default)
- Validate file contents before packaging
- Implement download tracking and analytics
- Ensure proper access controls on results container
- Add watermarking for premium features

## Branch Name
`feat/1.3-package-results-function`
