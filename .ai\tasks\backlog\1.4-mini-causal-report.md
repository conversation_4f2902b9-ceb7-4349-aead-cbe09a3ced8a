---
phase: "1.4"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 7
dependencies: ["1.1"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 1.4: Mini Causal Report Generator

## Agent Assignment: READY
**Estimated Complexity:** Medium (7 hours)
**Dependencies:** AML Pipeline Endpoint (1.1)
**Priority:** HIGH - Key differentiator for StudyScore

## Background Context
Generate a concise causality analysis report from processed document chunks using LLM analysis. This component queries the top-k most relevant chunks from Azure Cognitive Search, analyzes them for causal relationships, and produces a 300-word summary highlighting key interventions, outcomes, and confidence levels. This differentiates our service from basic OCR by providing domain-specific insights.

## Acceptance Criteria
- [ ] Query Azure Cognitive Search for top-k causal chunks
- [ ] LLM prompt engineering for causality analysis
- [ ] Generate structured 300-word causal summary
- [ ] Extract confidence scores and relationship types
- [ ] Handle documents with no clear causal relationships
- [ ] Include methodology and limitations section
- [ ] Save report as both TXT and JSON formats
- [ ] Integration with result packaging function

## Implementation Guide
**Code patterns to follow:** `.ai/rules/llm-integration-standards.md`
**Reference implementations:** `.ai/references/causal-analysis-patterns.md`
**Dependencies:** OpenAI SDK, Azure Cognitive Search SDK
**File locations:** 
- `functions/causal_report/__init__.py`
- `functions/causal_report/prompts.py`
- `functions/shared/search_client.py`
- `functions/shared/llm_client.py`

## Test Requirements
- Unit tests for prompt engineering
- Integration tests with Azure Cognitive Search
- Integration tests with OpenAI API
- Quality tests for generated reports
- Performance tests (response time < 30s)
- Edge case tests (no causal content)

## Definition of Done
- [ ] Function generates high-quality causal reports
- [ ] All tests passing (unit, integration, quality)
- [ ] LLM prompts optimized and validated
- [ ] Error handling for edge cases
- [ ] Performance benchmarks met
- [ ] Integration with packaging complete

## Technical Specifications
```python
# Causal Analysis Prompt Template
CAUSAL_ANALYSIS_PROMPT = """
You are an expert sports science researcher analyzing a research document for causal relationships.

Document chunks (ranked by relevance):
{chunks}

Task: Analyze these chunks and provide a structured causal analysis report.

Focus on:
1. Interventions (training methods, treatments, protocols)
2. Outcomes (performance improvements, injury prevention, physiological changes)
3. Causal relationships and their strength
4. Confidence levels based on study design and evidence quality

Format your response as:
## Key Causal Relationships
[List 3-5 main causal relationships with confidence scores]

## Interventions Analyzed
[Specific interventions mentioned in the study]

## Outcomes Measured
[Specific outcomes and their measurement methods]

## Methodology Assessment
[Brief assessment of study design strength]

## Limitations
[Key limitations affecting causal inference]

Keep the total response under 300 words while maintaining scientific accuracy.
"""
```

**Function Logic:**
```python
import azure.functions as func
from shared.search_client import SearchClient
from shared.llm_client import LLMClient

def main(req: func.HttpRequest) -> func.HttpResponse:
    document_id = req.params.get('document_id')
    
    # Query top-k chunks from Azure Cognitive Search
    search_client = SearchClient()
    chunks = search_client.get_causal_chunks(
        document_id=document_id,
        top_k=10,
        filter_causal=True
    )
    
    if not chunks:
        return generate_no_causal_content_report(document_id)
    
    # Generate causal analysis using LLM
    llm_client = LLMClient()
    report = llm_client.generate_causal_analysis(
        chunks=chunks,
        max_tokens=500,
        temperature=0.3
    )
    
    # Structure and save report
    structured_report = {
        "document_id": document_id,
        "causal_relationships": extract_relationships(report),
        "interventions": extract_interventions(report),
        "outcomes": extract_outcomes(report),
        "confidence_scores": extract_confidence_scores(report),
        "methodology_assessment": extract_methodology(report),
        "limitations": extract_limitations(report),
        "full_text": report,
        "generated_at": datetime.utcnow().isoformat()
    }
    
    # Save both formats
    save_report_txt(document_id, report)
    save_report_json(document_id, structured_report)
    
    return func.HttpResponse(json.dumps(structured_report))
```

**Environment Variables:**
- `OPENAI_API_KEY`
- `AZURE_SEARCH_ENDPOINT`
- `AZURE_SEARCH_KEY`
- `CAUSAL_ANALYSIS_INDEX`

## Notes for AI Agent
- Use GPT-4 for highest quality causal analysis
- Implement prompt engineering best practices
- Handle documents with no causal content gracefully
- Include confidence scoring for all relationships
- Validate LLM outputs for scientific accuracy
- Implement caching for repeated analyses
- Add quality metrics for generated reports
- Consider domain-specific fine-tuning

## Prompt Engineering Strategy
- Use few-shot examples for consistent formatting
- Include domain-specific terminology
- Emphasize evidence-based confidence scoring
- Handle multiple study designs appropriately
- Focus on sports science and exercise physiology
- Include methodology assessment criteria

## Quality Assurance
- Validate causal relationships against source text
- Check confidence scores for reasonableness
- Ensure scientific terminology accuracy
- Verify intervention-outcome mappings
- Test with various document types and quality levels

## Branch Name
`feat/1.4-mini-causal-report`
