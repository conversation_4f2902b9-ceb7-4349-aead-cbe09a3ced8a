---
phase: "2.1"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 6
dependencies: ["1.1"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 2.1: Expose Azure Cognitive Search API

## Agent Assignment: READY
**Estimated Complexity:** Medium (6 hours)
**Dependencies:** AML Pipeline Endpoint (1.1)
**Priority:** HIGH - Enables frontend search functionality

## Background Context
Create REST API endpoints that expose Azure Cognitive Search functionality for the StudyScore frontend. This includes research paper search, filtering, faceted search, and quality-based ranking. The API should provide the search capabilities needed for the frontend while maintaining security and performance.

## Acceptance Criteria
- [ ] REST API endpoint for research paper search
- [ ] Support for complex query syntax and filters
- [ ] Faceted search with quality tiers, study types, years
- [ ] Pagination and sorting capabilities
- [ ] Quality score-based ranking
- [ ] Search suggestions and autocomplete
- [ ] Rate limiting and authentication
- [ ] Comprehensive error handling

## Implementation Guide
**Code patterns to follow:** `.ai/rules/api-design-standards.md`
**Reference implementations:** `.ai/references/search-api-patterns.md`
**Dependencies:** Azure Cognitive Search SDK, FastAPI or Next.js API routes
**File locations:** 
- `src/app/api/search/route.ts` (Next.js approach)
- `api/search/endpoints.py` (FastAPI approach)
- `lib/search/client.ts`
- `lib/search/types.ts`

## Test Requirements
- Unit tests for search logic
- Integration tests with Azure Cognitive Search
- Performance tests (response time < 500ms)
- Security tests (authentication, rate limiting)
- Edge case tests (empty results, malformed queries)
- Load tests (concurrent search requests)

## Definition of Done
- [ ] Search API endpoints deployed and functional
- [ ] All tests passing (unit, integration, performance)
- [ ] Documentation with API specifications
- [ ] Rate limiting and security implemented
- [ ] Frontend integration ready
- [ ] Monitoring and logging configured

## Technical Specifications

**API Endpoints:**
```typescript
// Primary search endpoint
GET /api/search
  ?q={query}
  &filters={json}
  &sort={field}
  &page={number}
  &size={number}
  &facets={boolean}

// Search suggestions
GET /api/search/suggest
  ?q={partial_query}
  &size={number}

// Document details
GET /api/search/document/{document_id}

// Search facets
GET /api/search/facets
  ?q={query}
  &filters={json}
```

**Response Format:**
```json
{
  "results": [
    {
      "document_id": "uuid",
      "title": "Effects of HIIT on VO2 Max",
      "authors": ["Smith, J.", "Johnson, M."],
      "journal": "Journal of Sports Science",
      "year": 2024,
      "abstract": "This study investigated...",
      "quality_score": 87,
      "quality_tier": "tier1",
      "study_type": "randomized_controlled_trial",
      "sample_size": 24,
      "keywords": ["HIIT", "VO2 max", "cardiovascular"],
      "doi": "10.1234/jss.2024.001",
      "url": "https://example.com/paper",
      "relevance_score": 0.95
    }
  ],
  "total_count": 1247,
  "page": 1,
  "size": 20,
  "facets": {
    "quality_tier": {
      "tier1": 156,
      "tier2": 423,
      "moderate": 668
    },
    "study_type": {
      "randomized_controlled_trial": 234,
      "observational": 567,
      "systematic_review": 89
    },
    "year": {
      "2024": 45,
      "2023": 123,
      "2022": 234
    }
  },
  "suggestions": ["HIIT training", "VO2 max improvement"],
  "query_time_ms": 45
}
```

**Implementation (Next.js API Route):**
```typescript
// src/app/api/search/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { SearchClient } from '@/lib/search/client';
import { SearchParams, SearchResponse } from '@/lib/search/types';

export async function GET(request: NextRequest) {
  try {
    const searchParams = extractSearchParams(request.nextUrl.searchParams);
    
    const searchClient = new SearchClient();
    const results = await searchClient.search(searchParams);
    
    return NextResponse.json(results);
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Search failed', message: error.message },
      { status: 500 }
    );
  }
}

function extractSearchParams(params: URLSearchParams): SearchParams {
  return {
    query: params.get('q') || '',
    filters: JSON.parse(params.get('filters') || '{}'),
    sort: params.get('sort') || 'relevance',
    page: parseInt(params.get('page') || '1'),
    size: parseInt(params.get('size') || '20'),
    includeFacets: params.get('facets') === 'true'
  };
}
```

**Environment Variables:**
- `AZURE_SEARCH_ENDPOINT`
- `AZURE_SEARCH_KEY`
- `AZURE_SEARCH_INDEX_NAME`
- `SEARCH_API_RATE_LIMIT=100`

## Notes for AI Agent
- Use Azure Cognitive Search SDK for optimal performance
- Implement proper query sanitization and validation
- Add comprehensive logging for search analytics
- Use semantic search capabilities when available
- Implement caching for common queries
- Add search result ranking based on quality scores
- Include proper error handling for all edge cases
- Consider implementing search result personalization

## Search Features
- **Full-text search** across title, abstract, content
- **Faceted filtering** by quality tier, study type, year, journal
- **Quality-based ranking** with Tier 1 studies prioritized
- **Semantic search** for better relevance matching
- **Autocomplete** and search suggestions
- **Advanced filters** for sample size, methodology, outcomes

## Performance Optimization
- Implement result caching for common queries
- Use search result pagination efficiently
- Optimize facet calculations
- Add search analytics and monitoring
- Implement query performance tracking

## Branch Name
`feat/2.1-expose-search-api`
