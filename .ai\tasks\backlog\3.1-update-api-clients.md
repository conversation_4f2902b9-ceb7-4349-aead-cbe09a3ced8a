---
phase: "3.1"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 5
dependencies: ["2.1", "2.2"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 3.1: Update API Clients for Real Backend

## Agent Assignment: READY
**Estimated Complexity:** Medium (5 hours)
**Dependencies:** Search API (2.1), Graph API (2.2)
**Priority:** HIGH - Connects frontend to real data

## Background Context
Replace the existing mock API clients with real implementations that connect to the deployed Azure backend services. This includes updating the search client, graph client, and document processing client to use actual REST endpoints instead of mock data.

## Acceptance Criteria
- [ ] Update search client to use real Search API
- [ ] Update graph client to use real Graph API
- [ ] Update document processing client for upload/status
- [ ] Implement proper error handling and loading states
- [ ] Add request/response logging for debugging
- [ ] Implement retry logic and timeout handling
- [ ] Add TypeScript types for all API responses
- [ ] Update environment configuration

## Implementation Guide
**Code patterns to follow:** `.ai/rules/api-client-standards.md`
**Reference implementations:** `.ai/references/api-client-patterns.md`
**Dependencies:** Existing UI components, API endpoints from Phase 2
**File locations:** 
- `src/lib/api/search.ts` (update existing)
- `src/lib/api/graph.ts` (update existing)
- `src/lib/api/documents.ts` (update existing)
- `src/lib/api/types.ts` (update existing)

## Test Requirements
- Unit tests for API client functions
- Integration tests with real backend APIs
- Error handling tests (network failures, timeouts)
- Loading state tests
- Type safety validation tests
- Performance tests (response times)

## Definition of Done
- [ ] All API clients updated to use real endpoints
- [ ] All tests passing (unit, integration)
- [ ] Error handling working correctly
- [ ] Loading states implemented
- [ ] TypeScript types accurate
- [ ] Environment configuration complete

## Technical Specifications

**Updated Search Client:**
```typescript
// src/lib/api/search.ts
import { SearchParams, SearchResponse, SearchSuggestion } from './types';

export class SearchClient {
  private baseUrl: string;
  private apiKey?: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY;
  }

  async search(params: SearchParams): Promise<SearchResponse> {
    const url = new URL('/api/search', this.baseUrl);
    
    // Add query parameters
    if (params.query) url.searchParams.set('q', params.query);
    if (params.filters) url.searchParams.set('filters', JSON.stringify(params.filters));
    if (params.sort) url.searchParams.set('sort', params.sort);
    url.searchParams.set('page', params.page.toString());
    url.searchParams.set('size', params.size.toString());
    if (params.includeFacets) url.searchParams.set('facets', 'true');

    const response = await this.fetchWithRetry(url.toString());
    
    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`);
    }

    return response.json();
  }

  async getSuggestions(query: string): Promise<SearchSuggestion[]> {
    const url = new URL('/api/search/suggest', this.baseUrl);
    url.searchParams.set('q', query);
    url.searchParams.set('size', '10');

    const response = await this.fetchWithRetry(url.toString());
    
    if (!response.ok) {
      throw new Error(`Suggestions failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.suggestions || [];
  }

  private async fetchWithRetry(
    url: string, 
    options: RequestInit = {}, 
    retries = 3
  ): Promise<Response> {
    const headers = {
      'Content-Type': 'application/json',
      ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` }),
      ...options.headers,
    };

    for (let i = 0; i < retries; i++) {
      try {
        const response = await fetch(url, {
          ...options,
          headers,
          signal: AbortSignal.timeout(30000), // 30s timeout
        });

        if (response.ok || response.status < 500) {
          return response;
        }

        if (i === retries - 1) throw new Error(`HTTP ${response.status}`);
      } catch (error) {
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }

    throw new Error('Max retries exceeded');
  }
}

export const searchClient = new SearchClient();
```

**Updated Graph Client:**
```typescript
// src/lib/api/graph.ts
import { GraphExploreParams, GraphResponse, RelatedPapersParams } from './types';

export class GraphClient {
  private baseUrl: string;
  private apiKey?: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY;
  }

  async exploreFromDocument(params: GraphExploreParams): Promise<GraphResponse> {
    const url = new URL(`/api/graph/explore/${params.documentId}`, this.baseUrl);
    
    if (params.depth) url.searchParams.set('depth', params.depth.toString());
    if (params.relationshipTypes?.length) {
      url.searchParams.set('relationship_types', params.relationshipTypes.join(','));
    }
    if (params.limit) url.searchParams.set('limit', params.limit.toString());

    const response = await this.fetchWithRetry(url.toString());
    
    if (!response.ok) {
      throw new Error(`Graph exploration failed: ${response.statusText}`);
    }

    return response.json();
  }

  async getRelatedPapers(params: RelatedPapersParams): Promise<GraphResponse> {
    const url = new URL(`/api/graph/related/${params.documentId}`, this.baseUrl);
    
    if (params.similarityThreshold) {
      url.searchParams.set('similarity_threshold', params.similarityThreshold.toString());
    }
    if (params.limit) url.searchParams.set('limit', params.limit.toString());

    const response = await this.fetchWithRetry(url.toString());
    
    if (!response.ok) {
      throw new Error(`Related papers failed: ${response.statusText}`);
    }

    return response.json();
  }

  private async fetchWithRetry(url: string, retries = 3): Promise<Response> {
    // Similar implementation to SearchClient
    // ... (retry logic with timeout and error handling)
  }
}

export const graphClient = new GraphClient();
```

**Environment Variables:**
```env
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com
NEXT_PUBLIC_API_KEY=your_api_key_here
NEXT_PUBLIC_ENABLE_MOCK_DATA=false
```

## Notes for AI Agent
- Maintain backward compatibility with existing component interfaces
- Implement comprehensive error handling for all failure scenarios
- Add request/response logging for debugging purposes
- Use proper TypeScript types for all API interactions
- Implement loading states and progress indicators
- Add retry logic with exponential backoff
- Include proper timeout handling
- Consider implementing request caching for performance

## Error Handling Strategy
- Network errors: Retry with exponential backoff
- Authentication errors: Clear tokens and redirect to login
- Rate limiting: Implement backoff and user feedback
- Server errors: Log and show user-friendly messages
- Timeout errors: Allow user to retry or cancel

## Performance Considerations
- Implement request deduplication for rapid successive calls
- Add response caching for expensive operations
- Use AbortController for request cancellation
- Implement proper loading states to improve perceived performance
- Consider implementing optimistic updates where appropriate

## Branch Name
`feat/3.1-update-api-clients`
