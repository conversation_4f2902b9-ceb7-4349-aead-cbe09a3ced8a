---
phase: "3.2"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 6
dependencies: ["3.1"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 3.2: Connect UI to Real APIs

## Agent Assignment: READY
**Estimated Complexity:** Medium (6 hours)
**Dependencies:** Updated API Clients (3.1)
**Priority:** HIGH - Makes frontend fully functional

## Background Context
Update all React components to use the real API clients instead of mock data. This includes updating the search interface, research cards, statistics dashboard, and any other components that currently display placeholder data. Implement proper loading states, error handling, and data validation.

## Acceptance Criteria
- [ ] Search interface connected to real Search API
- [ ] Research cards display real paper data
- [ ] Statistics dashboard shows real metrics
- [ ] Quality badges reflect actual quality scores
- [ ] Loading states implemented for all async operations
- [ ] Error handling with user-friendly messages
- [ ] Empty states for no results scenarios
- [ ] Performance optimization for data loading

## Implementation Guide
**Code patterns to follow:** `.ai/rules/react-integration-standards.md`
**Reference implementations:** `.ai/references/api-integration-patterns.md`
**Dependencies:** Updated API clients (3.1), existing UI components
**File locations:** 
- `src/app/page.tsx` (update homepage)
- `src/components/features/SearchBar.tsx` (update)
- `src/components/features/ResearchCard.tsx` (update)
- `src/components/features/StatsCard.tsx` (update)
- `src/hooks/useSearch.ts` (create)
- `src/hooks/useResearchData.ts` (create)

## Test Requirements
- Unit tests for updated components
- Integration tests with real API data
- Loading state tests
- Error handling tests
- Empty state tests
- Performance tests (render times)

## Definition of Done
- [ ] All components using real API data
- [ ] All tests passing (unit, integration)
- [ ] Loading states working correctly
- [ ] Error handling implemented
- [ ] Performance benchmarks met
- [ ] User experience optimized

## Technical Specifications

**Updated Homepage with Real Data:**
```typescript
// src/app/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { searchClient } from '@/lib/api/search';
import { SearchResponse, ResearchPaper } from '@/lib/api/types';
import SearchBar from '@/components/features/SearchBar';
import ResearchCard from '@/components/features/ResearchCard';
import { StatsCard } from '@/components/features/StatsCard';

export default function Home() {
  const [featuredPaper, setFeaturedPaper] = useState<ResearchPaper | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load featured paper (highest quality recent paper)
      const featuredResult = await searchClient.search({
        query: '',
        filters: { quality_tier: 'tier1' },
        sort: 'year_desc',
        page: 1,
        size: 1,
        includeFacets: false
      });
      
      if (featuredResult.results.length > 0) {
        setFeaturedPaper(featuredResult.results[0]);
      }

      // Load statistics (could be from a separate stats API)
      const statsResult = await searchClient.search({
        query: '',
        filters: {},
        page: 1,
        size: 0,
        includeFacets: true
      });
      
      setStats({
        totalPapers: statsResult.total_count,
        tier1Papers: statsResult.facets?.quality_tier?.tier1 || 0,
        tier2Papers: statsResult.facets?.quality_tier?.tier2 || 0,
        recentPapers: statsResult.facets?.year?.[new Date().getFullYear()] || 0
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (query: string) => {
    // Navigate to search results page or update current view
    window.location.href = `/search?q=${encodeURIComponent(query)}`;
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage error={error} onRetry={loadInitialData} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header remains the same */}
      
      <main>
        {/* Hero Section */}
        <section className="bg-white py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Evidence-Based <span className="text-primary">Sports Science</span>
              <br className="hidden sm:block" />
              <span className="block sm:inline">Research at Your Fingertips</span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 mb-8 max-w-3xl mx-auto px-4 sm:px-0">
              Search through {stats?.totalPapers?.toLocaleString() || '47,000+'} peer-reviewed sports science papers with intelligent quality scoring 
              and discover research relationships through our knowledge graph.
            </p>
            
            <div className="max-w-4xl mx-auto mb-8">
              <SearchBar 
                onSearch={handleSearch}
                placeholder="Search research papers..."
              />
            </div>
          </div>
        </section>

        {/* Real Stats Section */}
        {stats && (
          <section className="py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <StatsCard 
                  title="Research Database"
                  stats={[
                    {
                      label: 'Total Papers',
                      value: stats.totalPapers.toLocaleString(),
                      icon: 'FileText',
                      color: 'primary'
                    },
                    {
                      label: 'Tier 1 Studies',
                      value: stats.tier1Papers.toLocaleString(),
                      icon: 'Award',
                      color: 'success'
                    },
                    {
                      label: 'Recent Papers',
                      value: stats.recentPapers.toLocaleString(),
                      icon: 'Calendar',
                      color: 'info'
                    }
                  ]}
                />
              </div>
            </div>
          </section>
        )}

        {/* Real Featured Research */}
        {featuredPaper && (
          <section className="py-12 px-4 sm:px-6 lg:px-8 bg-white">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Featured Research
              </h2>
              <ResearchCard 
                paper={featuredPaper}
                onBookmark={(id) => console.log('Bookmark:', id)}
                onCite={(id) => console.log('Cite:', id)}
              />
            </div>
          </section>
        )}
      </main>
    </div>
  );
}
```

**Custom Hooks for Data Management:**
```typescript
// src/hooks/useSearch.ts
import { useState, useCallback } from 'react';
import { searchClient } from '@/lib/api/search';
import { SearchParams, SearchResponse } from '@/lib/api/types';

export function useSearch() {
  const [results, setResults] = useState<SearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async (params: SearchParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await searchClient.search(params);
      setResults(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setResults(null);
    setError(null);
  }, []);

  return {
    results,
    loading,
    error,
    search,
    reset
  };
}
```

**Loading and Error Components:**
```typescript
// src/components/ui/LoadingSpinner.tsx
export function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
    </div>
  );
}

// src/components/ui/ErrorMessage.tsx
interface ErrorMessageProps {
  error: string;
  onRetry?: () => void;
}

export function ErrorMessage({ error, onRetry }: ErrorMessageProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Something went wrong
        </h2>
        <p className="text-gray-600 mb-4">{error}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
}
```

## Notes for AI Agent
- Implement proper loading states for all async operations
- Add comprehensive error handling with user-friendly messages
- Use React Suspense where appropriate for better UX
- Implement proper data validation for API responses
- Add performance monitoring for API calls
- Consider implementing data caching for better performance
- Use proper TypeScript types throughout
- Implement proper accessibility for loading and error states

## Performance Considerations
- Implement data caching to reduce API calls
- Use React.memo for expensive components
- Implement virtual scrolling for large result sets
- Add request deduplication for rapid successive calls
- Consider implementing optimistic updates

## User Experience Improvements
- Add skeleton loading states for better perceived performance
- Implement proper empty states with helpful messaging
- Add search suggestions and autocomplete
- Implement infinite scrolling for search results
- Add proper error recovery mechanisms

## Branch Name
`feat/3.2-connect-ui-real-apis`
