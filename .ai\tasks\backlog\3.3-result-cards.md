---
phase: "3.3"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 4
dependencies: ["3.2"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 3.3: Results Page with Real Data

## Agent Assignment: READY
**Estimated Complexity:** Medium (4 hours)
**Dependencies:** Connect UI to Real APIs (3.2)
**Priority:** HIGH - Completes processing workflow

## Background Context
Create a comprehensive results page that displays real processing status and provides access to completed pipeline outputs. Users need to see their upload progress, estimated completion time, and download their results package when ready. This completes the user journey from upload to download.

## Acceptance Criteria
- [ ] Real-time status updates using polling or WebSocket
- [ ] Progress indicator with estimated completion time
- [ ] Download button for completed results ZIP file
- [ ] Error handling and retry options for failed jobs
- [ ] Processing summary with key metrics from real data
- [ ] Responsive design (mobile-friendly)
- [ ] Share link functionality for results
- [ ] Results history for authenticated users

## Implementation Guide
**Code patterns to follow:** `.ai/rules/results-page-standards.md`
**Reference implementations:** `.ai/references/real-time-ui-patterns.md`
**Dependencies:** Real backend APIs, React hooks
**File locations:** 
- `src/app/results/[documentId]/page.tsx`
- `src/components/results/StatusIndicator.tsx`
- `src/components/results/DownloadCard.tsx`
- `src/hooks/useJobStatus.ts`

## Test Requirements
- Component tests for all UI states (loading, completed, error)
- Integration tests with real status API endpoints
- Error handling tests (network failures, expired links)
- Accessibility tests (screen readers, keyboard navigation)
- Performance tests (polling efficiency, memory leaks)
- Mobile responsiveness tests

## Definition of Done
- [ ] Results page implemented with all status states
- [ ] All tests passing (unit, integration, accessibility)
- [ ] Real-time updates working correctly
- [ ] Download functionality tested and working
- [ ] Mobile responsive design validated
- [ ] Error states handled gracefully

## Technical Specifications
```typescript
// src/app/results/[documentId]/page.tsx
interface ResultsPageProps {
  params: { documentId: string };
}

export default function ResultsPage({ params }: ResultsPageProps) {
  const { documentId } = params;
  const { status, loading, error, retry } = useJobStatus(documentId);

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} onRetry={retry} />;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Processing Results</h1>
      
      <StatusIndicator 
        status={status.status}
        progress={status.progress_percentage}
        estimatedCompletion={status.estimated_completion}
      />
      
      {status.status === 'completed' && (
        <DownloadCard
          downloadUrl={status.download_url}
          expiresAt={status.download_expires}
          processingTime={status.processing_time_seconds}
          summary={status.processing_summary}
        />
      )}
      
      {status.status === 'failed' && (
        <ErrorDisplay
          error={status.error_message}
          onRetry={() => retry(documentId)}
        />
      )}
    </div>
  );
}
```

**Real-time Status Hook:**
```typescript
// src/hooks/useJobStatus.ts
export function useJobStatus(documentId: string) {
  const [status, setStatus] = useState<JobStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/jobs/${documentId}/status`);
        if (!response.ok) throw new Error('Failed to fetch status');
        
        const statusData = await response.json();
        setStatus(statusData);
        
        // Stop polling if completed or failed
        if (['completed', 'failed'].includes(statusData.status)) {
          return;
        }
        
        // Continue polling
        setTimeout(pollStatus, 5000);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    pollStatus();
  }, [documentId]);

  const retry = useCallback(async (docId: string) => {
    try {
      await fetch(`/api/jobs/${docId}/retry`, { method: 'POST' });
      setError(null);
      setLoading(true);
      // Restart polling
    } catch (err) {
      setError('Retry failed');
    }
  }, []);

  return { status, loading, error, retry };
}
```

## Notes for AI Agent
- Use real API endpoints for status polling
- Implement proper loading states and skeleton screens
- Handle edge cases (expired download links, network timeouts)
- Include analytics tracking for user interactions
- Use TypeScript for type safety
- Implement proper error boundaries
- Follow accessibility guidelines (WCAG 2.1)
- Optimize for mobile-first responsive design

## User Experience Flow
1. **Page Load**: Show loading state, start status polling
2. **Queued**: Display queue position and estimated start time
3. **Processing**: Show progress bar with current pipeline step
4. **Completed**: Display download button and processing summary
5. **Failed**: Show error message with retry option
6. **Download**: Track download analytics, show success message

## Branch Name
`feat/3.3-result-cards`
