---
phase: "3.4"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 5
dependencies: ["3.2"]
assignee: "devin-ai"
created: 2024-01-15
priority: "HIGH"
---

# 🤖 Phase 3.4: Upload UI with Real Backend

## Agent Assignment: READY
**Estimated Complexity:** Medium (5 hours)
**Dependencies:** Connect UI to Real APIs (3.2)
**Priority:** HIGH - Enables document upload workflow

## Background Context
Create a user-friendly document upload interface that connects to the real backend processing pipeline. Users should be able to drag-and-drop PDFs, see upload progress, and receive processing status updates. This completes the user journey from upload to results.

## Acceptance Criteria
- [ ] Drag-and-drop PDF upload interface
- [ ] File validation (PDF only, size limits)
- [ ] Upload progress indicators
- [ ] Real-time processing status updates
- [ ] Error handling for upload failures
- [ ] Multiple file upload support
- [ ] Mobile-responsive design
- [ ] Integration with authentication system

## Implementation Guide
**Code patterns to follow:** `.ai/rules/upload-ui-standards.md`
**Reference implementations:** `.ai/references/file-upload-patterns.md`
**Dependencies:** Real backend APIs, file upload libraries
**File locations:** 
- `src/components/upload/UploadZone.tsx`
- `src/components/upload/FileProgress.tsx`
- `src/components/upload/ProcessingStatus.tsx`
- `src/hooks/useFileUpload.ts`

## Test Requirements
- Unit tests for upload components
- Integration tests with backend APIs
- File validation tests
- Progress tracking tests
- Error handling tests
- Mobile responsiveness tests

## Definition of Done
- [ ] Upload interface fully functional
- [ ] All tests passing (unit, integration)
- [ ] Real backend integration working
- [ ] Progress tracking operational
- [ ] Error handling implemented
- [ ] Mobile responsive design validated

## Technical Specifications
```typescript
// src/components/upload/UploadZone.tsx
interface UploadZoneProps {
  onUploadComplete: (documentId: string) => void;
  onUploadError: (error: string) => void;
  maxFileSize?: number; // in MB
  allowMultiple?: boolean;
}

export function UploadZone({ 
  onUploadComplete, 
  onUploadError, 
  maxFileSize = 10,
  allowMultiple = false 
}: UploadZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  
  const handleFileUpload = async (files: FileList) => {
    for (const file of Array.from(files)) {
      if (!validateFile(file)) continue;
      
      try {
        const documentId = await uploadFile(file, (progress) => {
          setUploadProgress(prev => ({ ...prev, [file.name]: progress }));
        });
        
        onUploadComplete(documentId);
      } catch (error) {
        onUploadError(`Failed to upload ${file.name}: ${error.message}`);
      }
    }
  };

  return (
    <div 
      className={cn(
        "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
        isDragOver ? "border-primary bg-primary/5" : "border-gray-300"
      )}
      onDragOver={(e) => {
        e.preventDefault();
        setIsDragOver(true);
      }}
      onDragLeave={() => setIsDragOver(false)}
      onDrop={(e) => {
        e.preventDefault();
        setIsDragOver(false);
        handleFileUpload(e.dataTransfer.files);
      }}
    >
      <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Upload Research Papers
      </h3>
      <p className="text-gray-600 mb-4">
        Drag and drop PDF files here, or click to browse
      </p>
      <Button onClick={() => fileInputRef.current?.click()}>
        Choose Files
      </Button>
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        multiple={allowMultiple}
        className="hidden"
        onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
      />
    </div>
  );
}
```

**File Upload API Integration:**
```typescript
// src/hooks/useFileUpload.ts
export function useFileUpload() {
  const uploadFile = async (
    file: File, 
    onProgress: (progress: number) => void
  ): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
      // Add progress tracking
    });
    
    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }
    
    const result = await response.json();
    return result.documentId;
  };

  return { uploadFile };
}
```

## Notes for AI Agent
- Implement proper file validation (PDF only, size limits)
- Add comprehensive error handling for all failure scenarios
- Use proper progress indicators for better UX
- Implement drag-and-drop with visual feedback
- Add file preview capabilities
- Consider implementing resume/retry for failed uploads
- Ensure mobile-responsive design
- Add accessibility features for screen readers

## User Experience Features
- **Drag-and-drop**: Intuitive file upload
- **Progress tracking**: Real-time upload and processing status
- **Error recovery**: Clear error messages and retry options
- **File validation**: Immediate feedback on invalid files
- **Multiple uploads**: Batch processing capability
- **Mobile support**: Touch-friendly interface

## Branch Name
`feat/3.4-upload-ui`
