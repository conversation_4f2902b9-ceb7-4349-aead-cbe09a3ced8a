# 🔄 Complete Backend-First Renumbering Plan

## 📊 **Current State Analysis**

### **Completed (in done/ folder):**
- 0.1: Project Setup
- 0.2: Initial Website

### **Existing Tickets to Renumber:**
- 0.3-breadboard-ia.md
- 0.4-scaffold-tooling.md  
- 0.5-initial-ui-stub.md
- 0.6-scaffold-api-clients.md
- 0.7-connect-ui-apis.md
- 0.8-blog-whiteboard-integrations.md
- 0.9-final-qa-deployment.md
- 1.1-auth-setup.md
- 1.1-deploy-aml-endpoint.md (NEW)
- 1.1-upload-ui.md
- 1.2-blob-trigger-fn.md
- 1.2-result-cards.md
- 1.2-upload-trigger-function.md (NEW)
- 1.3-package-results-function.md (NEW)
- 1.3-responsive-layout.md
- 1.4-filtering-system.md
- 1.4-mini-causal-report.md (NEW)
- 1.5-citation-export.md
- 1.8-basic-auth.md
- 1.9-billing-stub.md
- 2.1-expose-search-api.md (NEW)
- 2.2-expose-graph-api.md (NEW)
- 2.4-aad-b2c-auth.md
- 3.1-update-api-clients.md (NEW)
- 3.2-connect-ui-real-apis.md (NEW)

## 🎯 **New Backend-First Sequential Numbering**

### **Phase 0: Foundation & Tooling (Completed + Remaining Setup)**
- ✅ 0.1: Project Setup (DONE)
- ✅ 0.2: Initial Website (DONE)
- 0.3: Breadboard IA → **KEEP AS 0.3** (Foundation planning)
- 0.4: Scaffold Tooling → **KEEP AS 0.4** (Development tools)

### **Phase 1: Core Backend Infrastructure (CRITICAL PATH)**
- 1.1: Deploy AML Pipeline as REST Endpoint → **KEEP AS 1.1** ⭐ **START HERE**
- 1.2: Upload Trigger Function → **KEEP AS 1.2**
- 1.3: Package Results Function → **KEEP AS 1.3**
- 1.4: Mini Causal Report Generator → **KEEP AS 1.4**

### **Phase 2: Data APIs**
- 2.1: Expose Azure Cognitive Search API → **KEEP AS 2.1**
- 2.2: Expose Gremlin Graph API → **KEEP AS 2.2**
- 2.3: Document Details API → **NEW TICKET**
- 2.4: Unified Search Endpoint → **RENUMBER from 2.4-aad-b2c-auth.md**

### **Phase 3: Frontend Integration**
- 3.1: Update API Clients for Real Backend → **KEEP AS 3.1**
- 3.2: Connect UI to Real APIs → **KEEP AS 3.2**
- 3.3: Results Page with Real Data → **RENUMBER from 1.2-result-cards.md**
- 3.4: Upload UI with Real Backend → **RENUMBER from 1.1-upload-ui.md**

### **Phase 4: Enhanced UI Features**
- 4.1: Advanced Search Interface → **RENUMBER from 0.5-initial-ui-stub.md**
- 4.2: Responsive Layout → **RENUMBER from 1.3-responsive-layout.md**
- 4.3: Filtering System → **RENUMBER from 1.4-filtering-system.md**
- 4.4: Citation Export → **RENUMBER from 1.5-citation-export.md**

### **Phase 5: Authentication & User Management**
- 5.1: Basic Auth → **RENUMBER from 1.8-basic-auth.md**
- 5.2: Auth Setup → **RENUMBER from 1.1-auth-setup.md**
- 5.3: AAD B2C Auth → **RENUMBER from 2.4-aad-b2c-auth.md**

### **Phase 6: Integration & Deployment**
- 6.1: API Client Updates → **RENUMBER from 0.6-scaffold-api-clients.md**
- 6.2: UI API Connections → **RENUMBER from 0.7-connect-ui-apis.md**
- 6.3: Blog/Whiteboard Integrations → **RENUMBER from 0.8-blog-whiteboard-integrations.md**
- 6.4: Final QA & Deployment → **RENUMBER from 0.9-final-qa-deployment.md**

### **Phase 7: Business Features**
- 7.1: Billing System → **RENUMBER from 1.9-billing-stub.md**
- 7.2: Blob Trigger Function → **RENUMBER from 1.2-blob-trigger-fn.md**

## 🚀 **Renumbering Actions Required**

### **Files to Rename:**
1. `1.1-auth-setup.md` → `5.2-auth-setup.md`
2. `1.1-upload-ui.md` → `3.4-upload-ui.md`
3. `1.2-blob-trigger-fn.md` → `7.2-blob-trigger-fn.md`
4. `1.2-result-cards.md` → `3.3-result-cards.md`
5. `1.3-responsive-layout.md` → `4.2-responsive-layout.md`
6. `1.4-filtering-system.md` → `4.3-filtering-system.md`
7. `1.5-citation-export.md` → `4.4-citation-export.md`
8. `1.8-basic-auth.md` → `5.1-basic-auth.md`
9. `1.9-billing-stub.md` → `7.1-billing-stub.md`
10. `2.4-aad-b2c-auth.md` → `5.3-aad-b2c-auth.md`
11. `0.5-initial-ui-stub.md` → `4.1-advanced-search-interface.md`
12. `0.6-scaffold-api-clients.md` → `6.1-api-client-updates.md`
13. `0.7-connect-ui-apis.md` → `6.2-ui-api-connections.md`
14. `0.8-blog-whiteboard-integrations.md` → `6.3-blog-whiteboard-integrations.md`
15. `0.9-final-qa-deployment.md` → `6.4-final-qa-deployment.md`

### **Files to Update Content:**
- Update phase numbers in YAML frontmatter
- Update dependencies to reflect new sequence
- Update priorities based on backend-first approach

## 🎯 **Critical Path (Immediate Focus)**

### **Week 1-2: Backend Foundation**
1. **1.1-deploy-aml-endpoint.md** ⭐ **CRITICAL**
2. **1.2-upload-trigger-function.md** 🔥 **HIGH**
3. **1.3-package-results-function.md** 🔥 **HIGH**
4. **1.4-mini-causal-report.md** 🔥 **HIGH**

### **Week 3: Data APIs**
5. **2.1-expose-search-api.md** 🔥 **HIGH**
6. **2.2-expose-graph-api.md** 📊 **MEDIUM**

### **Week 4: Frontend Integration**
7. **3.1-update-api-clients.md** 🔥 **HIGH**
8. **3.2-connect-ui-real-apis.md** 🔥 **HIGH**

Everything else becomes Phase 4+ (enhancement features).

## 📋 **Next Steps**

1. **Execute renaming** of all files according to the plan above
2. **Update YAML frontmatter** in each file with new phase numbers
3. **Update dependencies** to reflect new sequence
4. **Create missing tickets** (2.3, 2.4 new content)
5. **Update BACKEND_FIRST_SEQUENCE.md** with complete plan

This creates a logical, sequential flow from backend infrastructure → data APIs → frontend integration → enhancements → auth → deployment → business features.
