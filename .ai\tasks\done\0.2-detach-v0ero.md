---
phase: 0.2
title: "Detach from V0ero & Local Import"
classification: AGENT_READY
complexity: Low
dependencies: [0.1]
---

# Detach from V0ero & Local Import

## Overview

Stop the auto-deploy sync, download your stub from V0ero, and import it into your local IDE so you can begin real-data work.

## Acceptance Criteria

- [ ] V0ero/Git Integration disabled
- [ ] Old Vercel deployment removed
- [ ] Code downloaded into `studyscore-website/`
- [ ] Git repo initialized locally
- [ ] First local commit created

## Implementation Guide

1. **Disconnect V0ero/Git-sync**

   ```text
   Project → Settings → Git Integration → Disconnect
   ```

2. **Delete Vercel Deployment**
   In Vercel dashboard, remove the active deployment for this project.

3. **Download stub code**

   * In V0ero UI, click "Download Code" → Unzip to `~/Projects/studyscore-website`.

4. **Initialize Git**

   ```bash
   cd studyscore-website
   git init
   git add .
   git commit -m "chore: import stub from V0ero"
   ```

5. **Verify**

   ```bash
   npm install
   npm run dev
   # confirm stub homepage loads at http://localhost:3000
   ```

## Test Requirements

1. **Git Repository Test**:
   - Verify git repository is initialized with initial commit
   - Confirm all files are tracked properly

2. **Development Server Test**:
   - Run `npm run dev` and verify the server starts without errors
   - Navigate to http://localhost:3000 and confirm the homepage loads correctly

## Definition of Done

- [ ] V0ero integration completely disconnected
- [ ] Local git repository initialized
- [ ] All project files committed to git
- [ ] Development server runs successfully
- [ ] Homepage renders without errors

## Branch Name

`feat/0.2-detach-v0ero`

## Notes for AI Agent

- This phase assumes the project was initially created with V0ero
- If the project was not created with V0ero, this phase can be skipped
- Ensure all dependencies are properly installed after import
