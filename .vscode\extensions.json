{"recommendations": ["biomejs.biome", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-eslint", "github.copilot", "github.copilot-chat", "ms-vscode.vscode-npm-script", "bradlc.vscode-tailwindcss", "usernamehw.errorlens", "gruntfuggly.todo-tree", "ms-vscode.vscode-typescript-next"], "unwantedRecommendations": ["hookyqr.beautify", "ms-vscode.vscode-typescript"]}