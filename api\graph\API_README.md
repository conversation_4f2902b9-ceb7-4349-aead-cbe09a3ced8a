# StudyScore Search API

FastAPI-based REST API that exposes Azure Cognitive Search functionality for the StudyScore platform.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r api_requirements.txt
```

### 2. Configure Environment

Copy the example environment file and configure your Azure credentials:

```bash
cp api/.env.example api/.env
```

Edit `api/.env` with your Azure Search credentials:

```env
AZURE_SEARCH_SERVICE_ENDPOINT=https://athlea-search-service.search.windows.net
AZURE_SEARCH_INDEX=docs-chunks-index-v3
AZURE_SEARCH_ADMIN_KEY=your_actual_key_here
```

### 3. Test the Connection

```bash
python test_api.py
```

### 4. Run the API

```bash
python run_api.py
```

The API will be available at:
- **API Base**: http://localhost:8000
- **Documentation**: http://localhost:8000/api/docs
- **Health Check**: http://localhost:8000/api/health

## 📚 API Endpoints

### Search Endpoints

#### `GET /api/search`
Search research papers with comprehensive filtering.

**Parameters:**
- `q` (optional): Search query text
- `filters` (optional): JSON string of filters
- `sort` (optional): Sort field (relevance, date, quality, title)
- `page` (optional): Page number (default: 1)
- `size` (optional): Results per page (default: 20, max: 100)
- `facets` (optional): Include facets (default: true)

**Example:**
```bash
curl "http://localhost:8000/api/search?q=futsal%20injury&page=1&size=10"
```

#### `GET /api/search/suggest`
Get search suggestions based on partial query.

**Parameters:**
- `q`: Partial query for suggestions
- `size` (optional): Number of suggestions (default: 5, max: 20)

**Example:**
```bash
curl "http://localhost:8000/api/search/suggest?q=injury&size=5"
```

#### `GET /api/search/document/{document_id}`
Get detailed information about a specific document.

**Example:**
```bash
curl "http://localhost:8000/api/search/document/a8eb82eb-829e-5eb1-b0b9-65dbad8fa897"
```

#### `GET /api/search/facets`
Get available facets for search filtering.

**Parameters:**
- `q` (optional): Search query for facet filtering
- `filters` (optional): JSON filters

**Example:**
```bash
curl "http://localhost:8000/api/search/facets?q=futsal"
```

### Graph Endpoints

#### `GET /api/graph/explore/{document_id}`
Explore the knowledge graph starting from a specific document.

**Parameters:**
- `document_id`: Source document ID to explore from
- `depth` (optional): Traversal depth (1-4, default: 2)
- `relationship_types` (optional): Comma-separated relationship types
- `limit` (optional): Maximum nodes to return (default: 50, max: 200)

**Example:**
```bash
curl "http://localhost:8000/api/graph/explore/a8eb82eb-829e-5eb1-b0b9-65dbad8fa897?depth=1&limit=10"
```

#### `GET /api/graph/related/{document_id}`
Find documents related through shared entities.

**Parameters:**
- `document_id`: Source document ID
- `similarity_threshold` (optional): Similarity threshold (0.0-1.0, default: 0.5)
- `limit` (optional): Maximum documents to return (default: 20, max: 100)

**Example:**
```bash
curl "http://localhost:8000/api/graph/related/a8eb82eb-829e-5eb1-b0b9-65dbad8fa897?limit=5"
```

#### `GET /api/graph/stats`
Get overall knowledge graph statistics.

**Example:**
```bash
curl "http://localhost:8000/api/graph/stats"
```

### Utility Endpoints

#### `GET /api/health`
Health check with service status.

#### `GET /`
Basic API status.

## 🔍 Search Features

### Full-Text Search
Search across title, abstract, and content fields with semantic search capabilities.

### Advanced Filtering
Filter by:
- **Sports**: Specific sports (futsal, soccer, basketball, etc.)
- **Functional Domains**: Training domains (strength, endurance, etc.)
- **Quality Tier**: Research quality (tier1, tier2, moderate)
- **Study Type**: Type of research (RCT, observational, etc.)
- **Publication Year**: Year ranges

### Quality-Based Ranking
Results are ranked by:
1. Search relevance score
2. Research quality score
3. Publication recency

### Faceted Search
Get counts for all available filter values to build dynamic filter interfaces.

## �️ **Knowledge Graph Features**

### Graph Exploration
- **Entity Discovery**: Find all entities extracted from research papers
- **Relationship Traversal**: Explore connections between concepts, methods, and outcomes
- **Multi-hop Navigation**: Traverse the graph at different depths (1-4 hops)
- **Entity Types**: Events, methods, equipment, authors, concepts, and more

### Graph Analytics
- **4,524+ vertices** and **1,141+ edges** in the knowledge graph
- **14+ research documents** with extracted entities
- **Entity type distribution**: Exercise patterns, injury prevention, training methods
- **Relationship mapping**: Studies, related_to, authored_by connections

## �📊 Response Format

### Search Response
```json
{
  "results": [
    {
      "document_id": "uuid",
      "title": "Effects of HIIT on VO2 Max",
      "authors": ["Smith, J.", "Johnson, M."],
      "journal": "Journal of Sports Science",
      "year": 2024,
      "abstract": "This study investigated...",
      "quality_score": 87,
      "quality_tier": "tier1",
      "study_type": "randomized_controlled_trial",
      "sample_size": 24,
      "keywords": ["HIIT", "VO2 max"],
      "doi": "10.1234/jss.2024.001",
      "url": "https://example.com/paper",
      "relevance_score": 0.95
    }
  ],
  "total_count": 1247,
  "page": 1,
  "size": 20,
  "facets": {
    "sports": {"futsal": 156, "soccer": 423},
    "quality_tier": {"tier1": 156, "tier2": 423},
    "study_type": {"randomized_controlled_trial": 234}
  },
  "query_time_ms": 45
}
```

## 🛠️ Development

### Running Tests
```bash
python test_api.py
```

### Development Mode
```bash
# Run with auto-reload
python run_api.py
```

### API Documentation
Visit http://localhost:8000/api/docs for interactive API documentation.

## 🔧 Configuration

### Environment Variables
- `AZURE_SEARCH_SERVICE_ENDPOINT`: Azure Search service URL
- `AZURE_SEARCH_INDEX`: Search index name (default: docs-chunks-index-v3)
- `AZURE_SEARCH_ADMIN_KEY`: Azure Search admin key
- `API_HOST`: API host (default: 0.0.0.0)
- `API_PORT`: API port (default: 8000)
- `LOG_LEVEL`: Logging level (default: INFO)

### CORS Configuration
Configure allowed origins in the FastAPI app for frontend integration.

## 🚨 Error Handling

The API returns structured error responses:

```json
{
  "error": "SearchError",
  "message": "Search failed: Invalid query syntax",
  "details": {
    "query": "malformed query",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 📈 Performance

- **Response Time**: < 500ms for typical searches
- **Concurrent Requests**: Supports multiple concurrent searches
- **Caching**: Implements result caching for common queries
- **Rate Limiting**: Configurable rate limiting for production use

## 🔐 Security

- **API Key Authentication**: Secure Azure Search access
- **Input Validation**: Comprehensive request validation
- **Error Sanitization**: Safe error messages without sensitive data
- **CORS Configuration**: Configurable cross-origin access

## 📝 Next Steps

1. **Test the API**: Run `python test_api.py` to verify everything works
2. **Frontend Integration**: Update frontend API clients to use these endpoints
3. **Graph API**: Implement Phase 2.2 (Gremlin Graph API)
4. **Production Deployment**: Configure for production environment
