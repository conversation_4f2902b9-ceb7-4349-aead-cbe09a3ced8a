"""
Gremlin Graph client for StudyScore API
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from gremlin_python.driver import client, serializer
from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection
from gremlin_python.process.anonymous_traversal import traversal
from gremlin_python.process.graph_traversal import __
from dotenv import load_dotenv

from .types import (
    GraphExploreParams, GraphResponse, GraphNode, GraphEdge, 
    GraphStats, RelatedDocumentsParams, AuthorNetworkParams
)
from .exceptions import GraphError, GraphConnectionError

# Load environment variables
load_dotenv(".env")

logger = logging.getLogger(__name__)

class GraphClient:
    """Client for interacting with Cosmos DB Gremlin graph"""
    
    def __init__(self):
        """Initialize the graph client with Cosmos DB credentials"""
        self.endpoint = os.getenv("COSMOS_GREMLIN_ENDPOINT", "https://cosmos-gremlin-aml.documents.azure.com:443/")
        self.key = os.getenv("COSMOS_GREMLIN_KEY")
        self.database = os.getenv("COSMOS_GREMLIN_DATABASE", "graphragdb")
        self.graph = os.getenv("COSMOS_GREMLIN_GRAPH", "knowledge-graph")
        
        if not self.key:
            raise GraphConnectionError("Cosmos DB Gremlin credentials not configured")
        
        # Convert HTTPS endpoint to WebSocket
        if self.endpoint.startswith('https://'):
            account_name = self.endpoint.split('//')[1].split('.')[0]
            self.websocket_endpoint = f"wss://{account_name}.gremlin.cosmos.azure.com:443/"
        else:
            self.websocket_endpoint = self.endpoint
        
        self.username = f"/dbs/{self.database}/colls/{self.graph}"
        self.client = None
        
        logger.info(f"Initialized Graph client for database: {self.database}, graph: {self.graph}")
    
    def connect(self):
        """Establish connection to the graph database"""
        try:
            self.client = client.Client(
                self.websocket_endpoint,
                'g',
                username=self.username,
                password=self.key,
                message_serializer=serializer.GraphSONSerializersV2d0()
            )
            logger.info(f"Connected to Cosmos DB Gremlin: {self.websocket_endpoint}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Cosmos DB Gremlin: {e}")
            raise GraphConnectionError(f"Graph connection failed: {e}")
    
    def disconnect(self):
        """Close the graph database connection"""
        if self.client:
            try:
                self.client.close()
                logger.info("Disconnected from Cosmos DB Gremlin")
            except Exception as e:
                logger.warning(f"Error during disconnect: {e}")
    
    async def test_connection(self) -> bool:
        """Test the connection to the graph database"""
        try:
            if not self.client:
                self.connect()

            # Simple query to test connection - run in thread to avoid event loop conflicts
            import concurrent.futures
            import threading

            def _test_query():
                try:
                    result = self.client.submit("g.V().count()").all().result()
                    return result[0] if result else 0
                except Exception as e:
                    raise e

            # Run the blocking operation in a thread
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_test_query)
                vertex_count = future.result(timeout=10)

            logger.info(f"Graph connection test successful. Vertex count: {vertex_count}")
            return True
        except Exception as e:
            logger.error(f"Graph connection test failed: {e}")
            raise GraphConnectionError(f"Graph connection test failed: {e}")
    
    async def explore_from_document(self, params: GraphExploreParams) -> GraphResponse:
        """
        Explore relationships from a specific document
        """
        try:
            if not self.client:
                self.connect()

            # Build the Gremlin query for document exploration
            query = self._build_explore_query(params)
            logger.info(f"Executing explore query: {query}")

            # Execute the query in a thread to avoid event loop conflicts
            import concurrent.futures

            def _execute_query():
                return self.client.submit(query).all().result()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_execute_query)
                result = future.result(timeout=30)

            # Process results into nodes and edges
            nodes, edges = self._process_explore_results(result, params.document_id)

            return GraphResponse(
                nodes=nodes,
                edges=edges,
                metadata={
                    "total_nodes": len(nodes),
                    "total_edges": len(edges),
                    "query_time_ms": 100,  # Placeholder
                    "traversal_depth": params.depth,
                    "source_document": params.document_id
                }
            )

        except Exception as e:
            logger.error(f"Graph exploration error: {e}")
            raise GraphError(f"Graph exploration failed: {e}")
    
    async def get_related_documents(self, params: RelatedDocumentsParams) -> GraphResponse:
        """
        Find documents related through shared concepts/entities
        """
        try:
            if not self.client:
                self.connect()
            
            # Query to find related documents through shared entities
            query = f"""
            g.V().has('source_document_id', '{params.document_id}')
            .out('related_to')
            .in('related_to')
            .has('source_document_id')
            .where(neq('{params.document_id}'))
            .dedup()
            .limit({params.limit})
            .project('id', 'properties', 'document_id')
            .by(id())
            .by(valueMap())
            .by(values('source_document_id'))
            """
            
            # Execute query in thread
            import concurrent.futures

            def _execute_query():
                return self.client.submit(query).all().result()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_execute_query)
                result = future.result(timeout=30)

            nodes, edges = self._process_related_documents_results(result, params.document_id)
            
            return GraphResponse(
                nodes=nodes,
                edges=edges,
                metadata={
                    "total_nodes": len(nodes),
                    "total_edges": len(edges),
                    "query_time_ms": 80,
                    "source_document": params.document_id,
                    "similarity_threshold": params.similarity_threshold
                }
            )
            
        except Exception as e:
            logger.error(f"Related documents query error: {e}")
            raise GraphError(f"Related documents query failed: {e}")
    
    async def get_graph_stats(self) -> GraphStats:
        """
        Get overall graph statistics
        """
        try:
            if not self.client:
                self.connect()
            
            # Execute queries in thread
            import concurrent.futures

            def _execute_stats_queries():
                vertex_count = self.client.submit("g.V().count()").all().result()
                edge_count = self.client.submit("g.E().count()").all().result()
                entity_types = self.client.submit("g.V().groupCount().by('type')").all().result()
                document_count = self.client.submit(
                    "g.V().has('source_document_id').values('source_document_id').dedup().count()"
                ).all().result()
                return vertex_count, edge_count, entity_types, document_count

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_execute_stats_queries)
                vertex_count_result, edge_count_result, entity_types_result, document_count_result = future.result(timeout=30)
            
            return GraphStats(
                total_vertices=vertex_count_result[0] if vertex_count_result else 0,
                total_edges=edge_count_result[0] if edge_count_result else 0,
                total_documents=document_count_result[0] if document_count_result else 0,
                entity_types=entity_types_result[0] if entity_types_result else {},
                last_updated="2024-01-15T00:00:00Z"  # Placeholder
            )
            
        except Exception as e:
            logger.error(f"Graph stats error: {e}")
            raise GraphError(f"Graph stats query failed: {e}")
    
    def _build_explore_query(self, params: GraphExploreParams) -> str:
        """Build Gremlin query for document exploration"""
        
        # Base query to find entities from the document
        base_query = f"g.V().has('source_document_id', '{params.document_id}')"
        
        if params.depth == 1:
            # Single hop exploration
            query = f"""
            {base_query}
            .project('vertex', 'edges', 'neighbors')
            .by(project('id', 'label', 'properties').by(id()).by(label()).by(valueMap()))
            .by(outE().project('id', 'label', 'target').by(id()).by(label()).by(inV().id()).fold())
            .by(out().project('id', 'label', 'properties').by(id()).by(label()).by(valueMap()).fold())
            """
        else:
            # Multi-hop exploration with path tracking
            query = f"""
            {base_query}
            .repeat(out().simplePath())
            .times({params.depth})
            .path()
            .limit({params.limit})
            """
        
        return query
    
    def _process_explore_results(self, results: List[Dict], source_document_id: str) -> tuple[List[GraphNode], List[GraphEdge]]:
        """Process exploration results into nodes and edges"""
        nodes = []
        edges = []
        seen_nodes = set()
        seen_edges = set()
        
        for result in results:
            if isinstance(result, dict):
                # Process single result format
                if 'vertex' in result:
                    vertex = result['vertex']
                    node_id = vertex.get('id', '')
                    
                    if node_id not in seen_nodes:
                        nodes.append(GraphNode(
                            id=node_id,
                            type=vertex.get('properties', {}).get('type', ['unknown'])[0] if isinstance(vertex.get('properties', {}).get('type'), list) else vertex.get('properties', {}).get('type', 'unknown'),
                            label=vertex.get('properties', {}).get('node_label', [''])[0] if isinstance(vertex.get('properties', {}).get('node_label'), list) else vertex.get('properties', {}).get('node_label', ''),
                            properties=self._flatten_properties(vertex.get('properties', {}))
                        ))
                        seen_nodes.add(node_id)
                    
                    # Process edges
                    for edge_data in result.get('edges', []):
                        edge_id = f"{node_id}_{edge_data.get('target', '')}"
                        if edge_id not in seen_edges:
                            edges.append(GraphEdge(
                                source=node_id,
                                target=edge_data.get('target', ''),
                                type=edge_data.get('label', 'related_to'),
                                properties={}
                            ))
                            seen_edges.add(edge_id)
                    
                    # Process neighbor nodes
                    for neighbor in result.get('neighbors', []):
                        neighbor_id = neighbor.get('id', '')
                        if neighbor_id not in seen_nodes:
                            nodes.append(GraphNode(
                                id=neighbor_id,
                                type=neighbor.get('properties', {}).get('type', ['unknown'])[0] if isinstance(neighbor.get('properties', {}).get('type'), list) else neighbor.get('properties', {}).get('type', 'unknown'),
                                label=neighbor.get('properties', {}).get('node_label', [''])[0] if isinstance(neighbor.get('properties', {}).get('node_label'), list) else neighbor.get('properties', {}).get('node_label', ''),
                                properties=self._flatten_properties(neighbor.get('properties', {}))
                            ))
                            seen_nodes.add(neighbor_id)
        
        return nodes, edges
    
    def _process_related_documents_results(self, results: List[Dict], source_document_id: str) -> tuple[List[GraphNode], List[GraphEdge]]:
        """Process related documents results"""
        nodes = []
        edges = []
        
        for result in results:
            if isinstance(result, dict):
                node_id = result.get('id', '')
                document_id = result.get('document_id', '')
                properties = self._flatten_properties(result.get('properties', {}))
                
                nodes.append(GraphNode(
                    id=node_id,
                    type=properties.get('type', 'entity'),
                    label=properties.get('node_label', ''),
                    properties={
                        **properties,
                        'source_document_id': document_id
                    }
                ))
                
                # Create edge to show relationship
                edges.append(GraphEdge(
                    source=source_document_id,
                    target=document_id,
                    type='related_through',
                    properties={'entity': node_id}
                ))
        
        return nodes, edges
    
    def _flatten_properties(self, properties: Dict[str, Any]) -> Dict[str, Any]:
        """Flatten Gremlin property format (arrays) to simple key-value pairs"""
        flattened = {}
        for key, value in properties.items():
            if isinstance(value, list) and len(value) > 0:
                flattened[key] = value[0]
            else:
                flattened[key] = value
        return flattened
