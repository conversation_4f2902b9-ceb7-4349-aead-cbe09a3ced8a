"""
Custom exceptions for StudyScore Graph API
"""

class GraphError(Exception):
    """Base exception for graph-related errors"""
    pass

class GraphConnectionError(GraphError):
    """Exception raised when connection to Cosmos DB Gremlin fails"""
    pass

class GraphQueryError(GraphError):
    """Exception raised for invalid graph queries"""
    pass

class GraphTimeoutError(GraphError):
    """Exception raised when graph operations timeout"""
    pass

class GraphTraversalError(GraphError):
    """Exception raised during graph traversal operations"""
    pass
