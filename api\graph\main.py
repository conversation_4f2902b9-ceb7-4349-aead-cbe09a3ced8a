"""
StudyScore Search API
FastAPI application that exposes Azure Cognitive Search functionality
"""

from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from typing import Optional, List, Dict, Any
import os
import logging
from dotenv import load_dotenv

from .search.client import SearchClient
from .search.types import SearchParams, SearchResponse, SearchSuggestion
from .search.exceptions import SearchError
from .graph.client import GraphClient
from .graph.types import (
    GraphExploreParams, GraphResponse, RelatedDocumentsParams,
    AuthorNetworkParams, GraphStats
)
from .graph.exceptions import GraphError

# Load environment variables from the main .env file
load_dotenv(".env")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="StudyScore Search API",
    description="API for searching sports science research papers using Azure Cognitive Search",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize clients
search_client = SearchClient()
graph_client = GraphClient()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"status": "StudyScore Search API is running", "version": "1.0.0"}

@app.get("/api/health")
async def health_check():
    """Detailed health check with service status"""
    try:
        # Test search client connection
        await search_client.test_connection()

        # Test graph client connection
        await graph_client.test_connection()

        return {
            "status": "healthy",
            "services": {
                "azure_search": "connected",
                "cosmos_gremlin": "connected",
                "api": "running"
            },
            "timestamp": "2024-01-15T00:00:00Z"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": "2024-01-15T00:00:00Z"
            }
        )

@app.get("/api/search", response_model=SearchResponse)
async def search_papers(
    q: Optional[str] = Query(None, description="Search query"),
    filters: Optional[str] = Query(None, description="JSON filters"),
    sort: Optional[str] = Query("relevance", description="Sort field"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Results per page"),
    facets: bool = Query(True, description="Include facets in response")
):
    """
    Search research papers with comprehensive filtering and faceting
    """
    try:
        search_params = SearchParams(
            query=q or "",
            filters=filters,
            sort=sort,
            page=page,
            size=size,
            include_facets=facets
        )
        
        results = await search_client.search(search_params)
        return results
        
    except SearchError as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected search error: {e}")
        raise HTTPException(status_code=500, detail="Search failed")

@app.get("/api/search/suggest")
async def search_suggestions(
    q: str = Query(..., description="Partial query for suggestions"),
    size: int = Query(5, ge=1, le=20, description="Number of suggestions")
):
    """
    Get search suggestions based on partial query
    """
    try:
        suggestions = await search_client.get_suggestions(q, size)
        return {"suggestions": suggestions}
        
    except Exception as e:
        logger.error(f"Suggestions error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get suggestions")

@app.get("/api/search/document/{document_id}")
async def get_document_details(document_id: str):
    """
    Get detailed information about a specific document
    """
    try:
        document = await search_client.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        return document
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document retrieval error: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve document")

@app.get("/api/search/facets")
async def get_search_facets(
    q: Optional[str] = Query(None, description="Search query for facet filtering"),
    filters: Optional[str] = Query(None, description="JSON filters")
):
    """
    Get available facets for search filtering
    """
    try:
        facets = await search_client.get_facets(q, filters)
        return {"facets": facets}
        
    except Exception as e:
        logger.error(f"Facets error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get facets")

# =============================================================================
# GRAPH API ENDPOINTS
# =============================================================================

@app.get("/api/graph/explore/{document_id}", response_model=GraphResponse)
async def explore_graph_from_document(
    document_id: str,
    depth: int = Query(2, ge=1, le=4, description="Traversal depth"),
    relationship_types: Optional[str] = Query(None, description="Comma-separated relationship types"),
    limit: int = Query(50, ge=1, le=200, description="Maximum nodes to return")
):
    """
    Explore the knowledge graph starting from a specific document
    """
    try:
        explore_params = GraphExploreParams(
            document_id=document_id,
            depth=depth,
            relationship_types=relationship_types.split(',') if relationship_types else [],
            limit=limit
        )

        result = await graph_client.explore_from_document(explore_params)
        return result

    except GraphError as e:
        logger.error(f"Graph exploration error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected graph error: {e}")
        raise HTTPException(status_code=500, detail="Graph exploration failed")

@app.get("/api/graph/related/{document_id}", response_model=GraphResponse)
async def get_related_documents(
    document_id: str,
    similarity_threshold: float = Query(0.5, ge=0.0, le=1.0, description="Similarity threshold"),
    limit: int = Query(20, ge=1, le=100, description="Maximum documents to return")
):
    """
    Find documents related to the given document through shared entities
    """
    try:
        params = RelatedDocumentsParams(
            document_id=document_id,
            similarity_threshold=similarity_threshold,
            limit=limit
        )

        result = await graph_client.get_related_documents(params)
        return result

    except GraphError as e:
        logger.error(f"Related documents error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected related documents error: {e}")
        raise HTTPException(status_code=500, detail="Related documents query failed")

@app.get("/api/graph/stats", response_model=GraphStats)
async def get_graph_statistics():
    """
    Get overall knowledge graph statistics
    """
    try:
        stats = await graph_client.get_graph_stats()
        return stats

    except GraphError as e:
        logger.error(f"Graph stats error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected graph stats error: {e}")
        raise HTTPException(status_code=500, detail="Graph statistics query failed")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
