#!/usr/bin/env python3
"""
Test script for StudyScore Search API
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from the main .env file
load_dotenv(".env")

from api.search.client import SearchClient
from api.search.types import SearchParams

async def test_search_client():
    """Test the search client functionality"""
    print("🧪 Testing StudyScore Search API Client")
    print("=" * 50)
    
    try:
        # Debug environment variables
        print("🔍 Debug: Environment variables:")
        print(f"   AZURE_SEARCH_SERVICE_ENDPOINT: {os.getenv('AZURE_SEARCH_SERVICE_ENDPOINT')}")
        print(f"   AZURE_SEARCH_INDEX: {os.getenv('AZURE_SEARCH_INDEX')}")
        print(f"   AZURE_SEARCH_ADMIN_KEY: {os.getenv('AZURE_SEARCH_ADMIN_KEY', 'NOT_SET')[:20]}...")

        # Initialize client
        print("\n1. Initializing search client...")
        client = SearchClient()
        print("✅ Search client initialized successfully")
        
        # Test connection
        print("\n2. Testing connection to Azure Search...")
        await client.test_connection()
        print("✅ Connection to Azure Search successful")
        
        # Test basic search
        print("\n3. Testing basic search...")
        search_params = SearchParams(
            query="futsal injury prevention",
            page=1,
            size=5,
            include_facets=True
        )
        
        results = await client.search(search_params)
        print(f"✅ Search completed successfully")
        print(f"   - Found {results.total_count} total results")
        print(f"   - Returned {len(results.results)} results on page {results.page}")
        print(f"   - Query time: {results.query_time_ms}ms")
        
        # Display first result
        if results.results:
            first_result = results.results[0]
            print(f"\n📄 First result:")
            print(f"   - Title: {first_result.title[:100]}...")
            print(f"   - Quality Score: {first_result.quality_score}")
            print(f"   - Relevance Score: {first_result.relevance_score}")
        
        # Display facets
        if results.facets:
            print(f"\n🏷️  Available facets:")
            if results.facets.sports:
                print(f"   - Sports: {list(results.facets.sports.keys())[:5]}")
            if results.facets.functional_domains:
                print(f"   - Domains: {list(results.facets.functional_domains.keys())[:5]}")
        
        # Test suggestions
        print("\n4. Testing search suggestions...")
        suggestions = await client.get_suggestions("injury", 3)
        print(f"✅ Got {len(suggestions)} suggestions")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion.text}")
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    success = await test_search_client()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
