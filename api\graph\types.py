"""
Type definitions for StudyScore Graph API
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

class GraphExploreParams(BaseModel):
    """Parameters for graph exploration requests"""
    document_id: str = Field(description="Source document ID to explore from")
    depth: int = Field(default=2, ge=1, le=4, description="Traversal depth (1-4)")
    relationship_types: List[str] = Field(default=[], description="Filter by relationship types")
    limit: int = Field(default=50, ge=1, le=200, description="Maximum nodes to return")

class RelatedDocumentsParams(BaseModel):
    """Parameters for finding related documents"""
    document_id: str = Field(description="Source document ID")
    similarity_threshold: float = Field(default=0.5, ge=0.0, le=1.0, description="Similarity threshold")
    limit: int = Field(default=20, ge=1, le=100, description="Maximum documents to return")

class AuthorNetworkParams(BaseModel):
    """Parameters for author network exploration"""
    author_id: str = Field(description="Author ID to explore from")
    depth: int = Field(default=2, ge=1, le=3, description="Network traversal depth")
    limit: int = Field(default=30, ge=1, le=100, description="Maximum authors to return")

class ConceptRelationParams(BaseModel):
    """Parameters for concept relationship queries"""
    concept: str = Field(description="Concept name to explore")
    strength_threshold: float = Field(default=0.3, ge=0.0, le=1.0, description="Relationship strength threshold")
    limit: int = Field(default=25, ge=1, le=100, description="Maximum related concepts")

class GraphNode(BaseModel):
    """Graph node representation"""
    id: str = Field(description="Unique node identifier")
    type: str = Field(description="Node type (entity, document, author, concept)")
    label: str = Field(description="Human-readable node label")
    properties: Dict[str, Any] = Field(default={}, description="Node properties")

class GraphEdge(BaseModel):
    """Graph edge representation"""
    source: str = Field(description="Source node ID")
    target: str = Field(description="Target node ID")
    type: str = Field(description="Edge type/relationship")
    properties: Dict[str, Any] = Field(default={}, description="Edge properties")

class GraphResponse(BaseModel):
    """Complete graph response"""
    nodes: List[GraphNode] = Field(description="Graph nodes")
    edges: List[GraphEdge] = Field(description="Graph edges")
    metadata: Dict[str, Any] = Field(description="Query metadata")

class GraphStats(BaseModel):
    """Graph statistics"""
    total_vertices: int = Field(description="Total number of vertices")
    total_edges: int = Field(description="Total number of edges")
    total_documents: int = Field(description="Total number of documents")
    entity_types: Dict[str, int] = Field(description="Entity type distribution")
    last_updated: str = Field(description="Last update timestamp")

class DocumentGraphSummary(BaseModel):
    """Summary of a document's graph connections"""
    document_id: str = Field(description="Document identifier")
    entity_count: int = Field(description="Number of entities extracted")
    relationship_count: int = Field(description="Number of relationships")
    connected_documents: int = Field(description="Number of connected documents")
    top_entities: List[str] = Field(description="Most important entities")
    entity_types: Dict[str, int] = Field(description="Entity type breakdown")

class AuthorCollaborationNetwork(BaseModel):
    """Author collaboration network data"""
    author_id: str = Field(description="Central author ID")
    collaborators: List[GraphNode] = Field(description="Collaborating authors")
    collaborations: List[GraphEdge] = Field(description="Collaboration relationships")
    network_metrics: Dict[str, float] = Field(description="Network analysis metrics")

class ConceptMap(BaseModel):
    """Concept relationship map"""
    central_concept: str = Field(description="Central concept")
    related_concepts: List[GraphNode] = Field(description="Related concepts")
    relationships: List[GraphEdge] = Field(description="Concept relationships")
    strength_distribution: Dict[str, int] = Field(description="Relationship strength distribution")

class GraphSearchParams(BaseModel):
    """Parameters for graph search queries"""
    query: str = Field(description="Search query")
    node_types: List[str] = Field(default=[], description="Filter by node types")
    limit: int = Field(default=20, ge=1, le=100, description="Maximum results")
    include_properties: bool = Field(default=True, description="Include node properties")

class GraphPathParams(BaseModel):
    """Parameters for finding paths between nodes"""
    source_id: str = Field(description="Source node ID")
    target_id: str = Field(description="Target node ID")
    max_depth: int = Field(default=4, ge=1, le=6, description="Maximum path length")
    relationship_types: List[str] = Field(default=[], description="Allowed relationship types")

class GraphPath(BaseModel):
    """Path between two nodes"""
    source: str = Field(description="Source node ID")
    target: str = Field(description="Target node ID")
    path_length: int = Field(description="Number of hops in path")
    nodes: List[GraphNode] = Field(description="Nodes in path")
    edges: List[GraphEdge] = Field(description="Edges in path")
    path_strength: float = Field(description="Overall path strength score")

class GraphCluster(BaseModel):
    """Graph cluster/community"""
    cluster_id: str = Field(description="Cluster identifier")
    nodes: List[GraphNode] = Field(description="Nodes in cluster")
    edges: List[GraphEdge] = Field(description="Internal cluster edges")
    cluster_metrics: Dict[str, float] = Field(description="Cluster analysis metrics")
    representative_nodes: List[str] = Field(description="Most representative nodes")

class GraphAnalytics(BaseModel):
    """Graph analytics and insights"""
    centrality_scores: Dict[str, float] = Field(description="Node centrality scores")
    community_structure: List[GraphCluster] = Field(description="Detected communities")
    key_bridges: List[GraphEdge] = Field(description="Important bridge connections")
    network_density: float = Field(description="Overall network density")
    average_path_length: float = Field(description="Average shortest path length")

class GraphVisualizationData(BaseModel):
    """Data formatted for graph visualization"""
    nodes: List[Dict[str, Any]] = Field(description="Nodes with visualization properties")
    edges: List[Dict[str, Any]] = Field(description="Edges with visualization properties")
    layout_config: Dict[str, Any] = Field(description="Layout configuration")
    color_scheme: Dict[str, str] = Field(description="Color mapping for node types")
    size_mapping: Dict[str, float] = Field(description="Size mapping for nodes")
