"""
Custom exceptions for StudyScore Search API
"""

class SearchError(Exception):
    """Base exception for search-related errors"""
    pass

class SearchConnectionError(SearchError):
    """Exception raised when connection to Azure Search fails"""
    pass

class SearchQueryError(SearchError):
    """Exception raised for invalid search queries"""
    pass

class SearchTimeoutError(SearchError):
    """Exception raised when search operations timeout"""
    pass
