"""
Type definitions for StudyScore Search API
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

class SearchParams(BaseModel):
    """Parameters for search requests"""
    query: str = Field(default="", description="Search query text")
    filters: Optional[str] = Field(default=None, description="JSON string of filters")
    sort: str = Field(default="relevance", description="Sort field")
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Results per page")
    include_facets: bool = Field(default=True, description="Include facets in response")

class SearchResult(BaseModel):
    """Individual search result"""
    document_id: str = Field(description="Unique document identifier")
    title: str = Field(description="Document title")
    authors: List[str] = Field(default=[], description="List of authors")
    journal: str = Field(default="", description="Journal name")
    year: int = Field(default=0, description="Publication year")
    abstract: str = Field(default="", description="Document abstract")
    quality_score: float = Field(default=0.0, description="Quality score (0-100)")
    quality_tier: str = Field(default="", description="Quality tier (tier1, tier2, moderate)")
    study_type: str = Field(default="", description="Type of study")
    sample_size: int = Field(default=0, description="Study sample size")
    keywords: List[str] = Field(default=[], description="Keywords/tags")
    doi: str = Field(default="", description="DOI identifier")
    url: str = Field(default="", description="Document URL")
    relevance_score: float = Field(default=0.0, description="Search relevance score")

class SearchFacets(BaseModel):
    """Search facets for filtering"""
    sports: Dict[str, int] = Field(default={}, description="Sports facets with counts")
    functional_domains: Dict[str, int] = Field(default={}, description="Functional domain facets")
    quality_tier: Dict[str, int] = Field(default={}, description="Quality tier facets")
    study_type: Dict[str, int] = Field(default={}, description="Study type facets")
    year: Dict[str, int] = Field(default={}, description="Publication year facets")

class SearchResponse(BaseModel):
    """Complete search response"""
    results: List[SearchResult] = Field(description="Search results")
    total_count: int = Field(description="Total number of matching documents")
    page: int = Field(description="Current page number")
    size: int = Field(description="Results per page")
    facets: Optional[SearchFacets] = Field(default=None, description="Search facets")
    query_time_ms: int = Field(description="Query execution time in milliseconds")

class SearchSuggestion(BaseModel):
    """Search suggestion item"""
    text: str = Field(description="Suggested search text")
    highlight: str = Field(description="Highlighted portion")

class DocumentDetail(BaseModel):
    """Detailed document information"""
    document_id: str = Field(description="Unique document identifier")
    title: str = Field(description="Document title")
    authors: List[str] = Field(default=[], description="List of authors")
    journal: str = Field(default="", description="Journal name")
    year: int = Field(default=0, description="Publication year")
    abstract: str = Field(default="", description="Document abstract")
    content: str = Field(default="", description="Full document content")
    quality_score: float = Field(default=0.0, description="Quality score (0-100)")
    quality_tier: str = Field(default="", description="Quality tier")
    study_type: str = Field(default="", description="Type of study")
    sample_size: int = Field(default=0, description="Study sample size")
    keywords: List[str] = Field(default=[], description="Keywords/tags")
    doi: str = Field(default="", description="DOI identifier")
    url: str = Field(default="", description="Document URL")
    sports: List[str] = Field(default=[], description="Related sports")
    functional_domains: List[str] = Field(default=[], description="Functional domains")
    detected_behaviors: List[str] = Field(default=[], description="Detected behaviors")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")

class ErrorResponse(BaseModel):
    """Error response format"""
    error: str = Field(description="Error type")
    message: str = Field(description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")

class HealthResponse(BaseModel):
    """Health check response"""
    status: str = Field(description="Service status")
    services: Dict[str, str] = Field(description="Individual service statuses")
    timestamp: str = Field(description="Check timestamp")
