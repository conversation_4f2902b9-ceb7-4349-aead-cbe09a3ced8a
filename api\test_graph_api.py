#!/usr/bin/env python3
"""
Test script for StudyScore Graph API
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from the main .env file
load_dotenv(".env")

from api.graph.client import GraphClient
from api.graph.types import GraphExploreParams, RelatedDocumentsParams

async def test_graph_client():
    """Test the graph client functionality"""
    print("🧪 Testing StudyScore Graph API Client")
    print("=" * 50)
    
    try:
        # Initialize client
        print("1. Initializing graph client...")
        client = GraphClient()
        print("✅ Graph client initialized successfully")
        
        # Test connection
        print("\n2. Testing connection to Cosmos DB Gremlin...")
        await client.test_connection()
        print("✅ Connection to Cosmos DB Gremlin successful")
        
        # Test graph statistics
        print("\n3. Getting graph statistics...")
        stats = await client.get_graph_stats()
        print(f"✅ Graph stats retrieved successfully")
        print(f"   - Total vertices: {stats.total_vertices}")
        print(f"   - Total edges: {stats.total_edges}")
        print(f"   - Total documents: {stats.total_documents}")
        print(f"   - Entity types: {list(stats.entity_types.keys())[:5]}")
        
        # Test document exploration
        print("\n4. Testing document exploration...")
        futsal_doc_id = "a8eb82eb-829e-5eb1-b0b9-65dbad8fa897"
        explore_params = GraphExploreParams(
            document_id=futsal_doc_id,
            depth=1,
            limit=10
        )
        
        exploration_result = await client.explore_from_document(explore_params)
        print(f"✅ Document exploration completed")
        print(f"   - Found {len(exploration_result.nodes)} nodes")
        print(f"   - Found {len(exploration_result.edges)} edges")
        print(f"   - Traversal depth: {exploration_result.metadata.get('traversal_depth', 'N/A')}")
        
        # Display some nodes
        if exploration_result.nodes:
            print(f"\n📊 Sample entities from document:")
            for i, node in enumerate(exploration_result.nodes[:5], 1):
                print(f"   {i}. {node.label} ({node.type})")
        
        # Test related documents
        print("\n5. Testing related documents query...")
        related_params = RelatedDocumentsParams(
            document_id=futsal_doc_id,
            similarity_threshold=0.3,
            limit=5
        )
        
        related_result = await client.get_related_documents(related_params)
        print(f"✅ Related documents query completed")
        print(f"   - Found {len(related_result.nodes)} related entities")
        print(f"   - Found {len(related_result.edges)} relationships")
        
        # Display related documents
        if related_result.nodes:
            print(f"\n🔗 Related entities:")
            for i, node in enumerate(related_result.nodes[:3], 1):
                doc_id = node.properties.get('source_document_id', 'Unknown')
                print(f"   {i}. {node.label} (from doc: {doc_id[:8]}...)")
        
        print("\n🎉 All graph tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up connection
        if 'client' in locals():
            client.disconnect()

async def main():
    """Main test function"""
    success = await test_graph_client()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
