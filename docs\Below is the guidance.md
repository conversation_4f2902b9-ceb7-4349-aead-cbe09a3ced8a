Below is the Part 1 guidance, in the same “LLM instruction” style as before—fully detailed, step-by-step, with code-level examples.

---

## 1. Detach from V0ero / Vercel

1. **Open your V0ero dashboard** and locate your site.
2. **Take screenshots** of each page you’ve stubbed out (for reference).
3. **Stop the automatic sync**:

   * In Project → Settings → Git Integration, click **Disconnect** (or delete the linked repo).
4. **Delete the Vercel deployment**:

   * In Vercel dashboard, under Deployments, delete the active deployment for this project.

> You’ve now “un-V0’d” your site—no more auto-deploy, no more hidden config.

---

## 2. Breadboarding (Information Architecture)

Create `docs/breadboard.md` and map routes + affordances:

```md
# Information Architecture

- `/`  
  - **ActivityCard** (static stub → will display weekly running goal)  
  - **SocialLinks** (external LinkTree style)  
  - **ProjectLinks** (cards linking out)  

- `/blog`  
  - **BlogIndex** (list of articles)  

- `/blog/[slug]`  
  - **BlogPost**  
    - Content (from MDX or markdown)  
    - Table of Contents (TOC)  
    - View counter (to implement)  

- `/whiteboards`  
  - **WhiteboardCanvas** (Embed TLDraw / Excalidraw)  

- **Footer**  
  - Internal links: About, Blog, Whiteboards  
  - External links: socials, “Built with…”  
```

---

## 3. Stub UI in V0ero

1. **Drag-and-drop** your IA sketch into V0ero’s visual editor.
2. **Place placeholder components** for each route (cards, lists, embeds).
3. **Publish** the stub (15 min of work) so you can see it live at your domain (e.g. `parkerx.com`).
4. **Verify** all links are “fake” stubs pointing to `#` or external placeholders.

> This ensures your design is pinned before adding real data.

---

## 4. Download & Open Locally

1. In V0ero’s dashboard, click **Download Code**.
2. Unzip to `~/Projects/px.com`.
3. Open a terminal there and run your editor (e.g. `code .`).

---

## 5. Identify 3rd-Party Integrations

Decide which APIs you’ll hook up:

* **GitHub API** → repo stargazers, commit metadata
* **Strava API** → athlete stats, weekly mileage
* **YouTube API** → channel stats (views, subscribers)
* **Excalidraw/TLDraw** → whiteboard embed (coming soon—out of scope)

Also choose your data-fetching stack:

* **Next.js Server Actions** (simplest)
* **tRPC + React Query** (for “VIV2” community; optional)

---

## 6. Align Your `tsconfig.json` to a Reference

Grab a proven TSConfig (e.g. from your “midday” repo) and replace V0’s:

```jsonc
// tsconfig.json
{
  "compilerOptions": {
    "target":      "es2023",
    "module":      "esnext",
    "lib":         ["dom", "dom.iterable", "esnext"],
    "jsx":         "preserve",
    "strict":      true,
    "baseUrl":     ".",
    "paths":       { "@/*": ["src/*"] },
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["next-env.d.ts", "src/**/*.ts", "src/**/*.tsx"],
  "exclude": ["node_modules"]
}
```

1. **Save** and in VS Code hit **“Restart TS Server.”**
2. **Remove** any unwanted V0-specific settings (e.g. legacy aliases).

---

## 7. Update `package.json`

1. **Rename** project in `package.json`:

   ```diff
   - "name": "v0ero-project"
   + "name": "px.com"
   ```
2. **Add scripts** for dev, build, clean:

   ```jsonc
   {
     "scripts": {
       "dev":    "next dev",
       "build":  "next build",
       "start":  "next start",
       "clean":  "rimraf node_modules",
       "install": "pnpm install"
     }
   }
   ```
3. **(Optional) Add Turborepo** for monorepo support:

   ```bash
   pnpm add -D turbo
   ```

   Then add `turbo.json` and update scripts:

   ```jsonc
   {
     "scripts": {
       "dev":   "turbo run dev",
       "build": "turbo run build"
     }
   }
   ```

---

## 8. Initialize Git & Fresh Install

```bash
git init
git add .
git commit -m "chore: initial import from V0ero"
pnpm clean
pnpm install
```

> You now have a clean slate and reproducible `node_modules`.

---

## 9. Install & Configure Biome (Formatter + Linter)

1. **Install**

   ```bash
   pnpm add -D biome
   ```
2. **Initialize**

   ```bash
   pnpm biome init
   ```
3. **Example `biome.json`** (at project root):

   ```jsonc
   {
     "extends": ["@biomejs/standard"],
     "files": ["src/**/*.{js,jsx,ts,tsx}"],
     "formatter": {
       "indentStyle": "space",
       "indentSize": 2
     }
   }
   ```
4. **Add lint script**:

   ```json
   { "scripts": { "lint": "biome" } }
   ```

---

## 10. Set Up VS Code Workspace

Create `.vscode/settings.json`:

```jsonc
{
  "typescript.tsdk":           "node_modules/typescript/lib",
  "editor.defaultFormatter":   "biome.biome",
  "editor.codeActionsOnSave": {
    "source.fixAll":          true,
    "source.organizeImports": true
  }
}
```

And `.vscode/extensions.json`:

```json
{
  "recommendations": [
    "biomejs.biome",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode"
  ]
}
```

> On opening the project, you’ll be prompted to install/enable these.

---

## 11. Establish Folder Structure for Tasks & References

In your repo root, create:

```
/src
/tasks
  backlog.md
  doing.md
  done.md
/references
/llms
```

* **`tasks/`**: track backlog → doing → done (for sprint tickets).
* **`references/`**: store architecture docs, API specs, example repos.
* **`llms/`**: local context files or prompts you’ll reuse.

---

## 12. First Smoke Test & Commit

1. **Run**

   ```bash
   pnpm dev
   ```
2. **Verify**: no TypeScript errors, Biome warnings cleaned up, the stub UI renders.
3. **Commit**

   ```bash
   git add .
   git commit -m "chore: scaffold project, tooling, IA stub"
   ```

---

You’ve now completed **Video 1**: unstub from V0ero, mapped your IA, scaffolded local tooling, and prepared for real data integration. In **Video 2**, you’ll hook up each API (GitHub, Strava, YouTube), implement server actions or tRPC calls, and build out real-data components.





PART 2


Here are the next logical phases you haven’t yet captured. For each I’ve given you the same front-matter + outline format. You can slot these into your `roadmap.md` immediately after **phase 0.1**.

---

phase: 0.2
title: "Detach from V0ero & Local Import"
classification: AGENT\_READY
complexity: Low
dependencies: \[0.1]

---

# Detach from V0ero & Local Import

## Overview

Stop the auto-deploy sync, download your stub from V0ero, and import it into your local IDE so you can begin real-data work.

## Acceptance Criteria

* [ ] V0ero/Cli Integration disabled
* [ ] Old Vercel deployment removed
* [ ] Code downloaded into `studyscore-website/`
* [ ] Git repo initialized locally
* [ ] First local commit created

## Implementation Guide

1. **Disconnect V0ero/Git-sync**

   ```text
   Project → Settings → Git Integration → Disconnect
   ```
2. **Delete Vercel Deployment**
   In Vercel dashboard, remove the active deployment for this project.
3. **Download stub code**

   * In V0ero UI, click “Download Code” → Unzip to `~/Projects/studyscore-website`.
4. **Initialize Git**

   ```bash
   cd studyscore-website
   git init
   git add .
   git commit -m "chore: import stub from V0ero"
   ```
5. **Verify**

   ```bash
   npm install
   npm run dev
   # confirm stub homepage loads at http://localhost:3000
   ```

---

phase: 0.3
title: "Breadboard Site Information Architecture"
classification: AGENT\_READY
complexity: Low
dependencies: \[0.2]

---

# Breadboard Site Information Architecture

## Overview

Define all routes and page affordances in a concise IA document so UI stubs match your end goals.

## Acceptance Criteria

* [ ] `docs/breadboard.md` created
* [ ] All major routes listed with their components/links
* [ ] External vs. internal navigation clearly marked

## Implementation Guide

1. **Create** `docs/breadboard.md`
2. **List routes** (home, blog, projects, whiteboards, footer)
3. **Under each**, annotate which UI pieces (cards, tables, embeds) will appear
4. **Mark** whether links are external (`target="_blank"`) or internal

---

phase: 0.4
title: "Scaffold Core Tooling & Codebase Layout"
classification: AGENT\_READY
complexity: Medium
dependencies: \[0.3]

---

# Scaffold Core Tooling & Codebase Layout

## Overview

Install & configure TypeScript, Next.js, Tailwind, Biome, VS Code settings—and establish your `src/` folder skeleton.

## Acceptance Criteria

* [ ] `tsconfig.json`, `next.config.mjs`, `tailwind.config.ts`, `postcss.config.mjs`, `biome.json` created per spec
* [ ] `.vscode/settings.json` & `extensions.json` added
* [ ] Folder structure under `src/` matches the spec:

  ```
  src/
    ├── app/
    ├── components/
    ├── lib/
    ├── hooks/
    └── types/
  ```
* [ ] `npm run dev`, `npm run lint`, `npm run typecheck` all pass

## Implementation Guide

1. **Copy** configuration files from phase 0.1 into project root.
2. **Run**

   ```bash
   npm install
   npm run typecheck
   npm run lint
   ```
3. **Add** VS Code workspace config under `.vscode/`

---

phase: 0.5
title: "Initial UI Stub & Layout"
classification: AGENT\_READY
complexity: Low
dependencies: \[0.4]

---

# Initial UI Stub & Layout

## Overview

Implement your global layout, basic navbar/footer, and the homepage stub from V0ero in real React+TSX.

## Acceptance Criteria

* [ ] `src/app/layout.tsx` with `<html>`, `<body>` and global CSS import
* [ ] `src/app/page.tsx` renders stub welcome screen
* [ ] Stub components for SocialLinks, ProjectCards, ActivityCard created under `components/`
* [ ] Tailwind styles applied (container, spacing, colors)

## Implementation Guide

1. **Create** `layout.tsx` and `page.tsx` per phase 0.1 examples.
2. **Under** `src/components/ui/` stub out:

   * `SocialLinks.tsx`
   * `ProjectLinks.tsx`
   * `ActivityCard.tsx`
3. **Import** and place them in `page.tsx`.
4. **Verify** `npm run dev` shows stub UI without errors.

---

phase: 0.6
title: "Scaffold Third-Party API Clients"
classification: AGENT\_READY
complexity: Medium
dependencies: \[0.4]

---

# Scaffold Third-Party API Clients

## Overview

Create typed fetch helpers for GitHub, Strava, and YouTube under `src/lib/api`.

## Acceptance Criteria

* [ ] `src/lib/api/github.ts`
* [ ] `src/lib/api/strava.ts`
* [ ] `src/lib/api/youtube.ts`
* [ ] All exports fully typed
* [ ] Environment variables referenced via `process.env.*`

## Implementation Guide

1. **Under** `src/lib/api/`, create each `.ts` file per earlier examples.
2. **Define** interfaces for responses.
3. **Implement** fetch functions with `Authorization` headers / API keys.
4. **Add** `.env.example` entries for each API key.

---

phase: 0.7
title: "Connect UI to APIs (Data Fetching)"
classification: AGENT\_READY
complexity: Medium
dependencies: \[0.5, 0.6]

---

# Connect UI to APIs (Data Fetching)

## Overview

Wire your stub components to real data using either Next.js Server Actions or tRPC + React Query.

## Acceptance Criteria

* [ ] Home page shows real GitHub ⭐️ count
* [ ] ActivityCard fetches & displays Strava weekly mileage
* [ ] YouTubeStats component shows channel subscriber count
* [ ] Loading and error states handled

## Implementation Guide

1. **Choose** data-fetching strategy:

   * a) **Server Actions**: export `async function getGitHubRepo()` in `src/app/actions.ts`
   * b) **tRPC**: set up `src/server/trpc.ts` and `src/server/routers`
2. **In** each component, call your helper and render the result.
3. **Test** with real API keys in local `.env`.
4. **Verify** `npm run dev` renders real data.

---

phase: 0.8
title: "Add Blog & Whiteboard Integrations"
classification: AGENT\_READY
complexity: Medium
dependencies: \[0.7]

---

# Add Blog & Whiteboard Integrations

## Overview

Implement the blog index/\[slug] pages via MDX, plus a whiteboard embed using Excalidraw or TLDraw.

## Acceptance Criteria

* [ ] `src/app/blog/page.tsx` lists MDX posts from `/posts/`
* [ ] `src/app/blog/[slug]/page.tsx` renders MDX content + TOC + view counter
* [ ] `/whiteboards` route embeds Excalidraw iframe
* [ ] Fallback UI for when embed script fails

## Implementation Guide

1. **Install** `@next/mdx` and configure in `next.config.mjs`
2. **Place** `.mdx` files under `posts/`, each with front-matter (title, date)
3. **Implement** file-system loader in `lib/mdx.ts`
4. **Embed** Excalidraw via `<iframe src="https://excalidraw.com/...">`.

---

phase: 0.9
title: "Final QA & Deployment"
classification: AGENT\_READY
complexity: Low
dependencies: \[0.8]

---

# Final QA & Deployment

## Overview

Clean up, run all checks, push to GitHub, and deploy to Vercel (or your chosen host).

## Acceptance Criteria

* [ ] `npm run lint`, `npm run typecheck`, `npm run format` all pass
* [ ] All pages render with real data (no stubs)
* [ ] New GitHub repo created & code pushed
* [ ] Vercel (or Netlify) configured and deployment successful

## Implementation Guide

1. **Review** todo comments and remove stubs.
2. **Run**

   ```bash
   npm run lint
   npm run typecheck
   npm run build
   ```
3. **Push** to `main` on GitHub.
4. **Connect** to Vercel, set environment variables, and deploy.

---

Insert these phases in order, adjust dependencies as you go, and you’ll have a complete, step-by-step roadmap from zero to a live, fully-functional Next.js site.
