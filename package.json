{"name": "studyscore-website", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome lint ./src", "format": "biome format --write .", "typecheck": "tsc --noEmit", "check": "biome check --write .", "clean": "git clean -xdf node_modules .next"}, "dependencies": {"@ai-sdk/vercel": "^0.0.1", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tooltip": "^1.0.7", "@vercel/mcp-adapter": "^0.10.0", "@vercel/sdk": "^1.8.1", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "dotenv": "^16.5.0", "lucide-react": "^0.408.0", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.63"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "20.11.20", "@types/react": "18.2.57", "autoprefixer": "^10.4.21", "eslint-config-next": "14.2.0", "typescript": "^5.8.3"}}