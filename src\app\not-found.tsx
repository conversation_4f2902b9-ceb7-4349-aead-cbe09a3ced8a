import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Home, BookOpen, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="text-center">
          <CardHeader className="pb-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="h-8 w-8 text-primary" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Research Not Found
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <p className="text-gray-600">
                The research paper or page you're looking for doesn't exist in our database.
              </p>
              <p className="text-sm text-gray-500">
                Error 404 - Page not found
              </p>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">What you can do:</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-primary" />
                  Try searching for different keywords
                </li>
                <li className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-primary" />
                  Browse our research categories
                </li>
                <li className="flex items-center gap-2">
                  <Home className="h-4 w-4 text-primary" />
                  Return to the homepage
                </li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button asChild className="flex-1">
                <Link href="/">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Link>
              </Button>
              
              <Button variant="outline" asChild className="flex-1">
                <Link href="/search">
                  <Search className="h-4 w-4 mr-2" />
                  Search Research
                </Link>
              </Button>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                Can't find what you're looking for? 
                <Link href="/contact" className="text-primary hover:underline ml-1">
                  Contact our research team
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
