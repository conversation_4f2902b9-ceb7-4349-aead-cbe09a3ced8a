import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Search, Award, FileText, Users, BarChart3 } from 'lucide-react';

// Sample research paper data
const samplePaper = {
  id: '1',
  title: 'Effects of High-Intensity Interval Training on VO2 Max in Elite Soccer Players',
  authors: [
    { name: '<PERSON>, J.', affiliation: 'University of Sports Science' },
    { name: '<PERSON>, M.', affiliation: 'Athletic Performance Lab' },
    { name: '<PERSON>, K<PERSON>', affiliation: 'Sports Medicine Institute' }
  ],
  journal: 'Journal of Sports Science',
  year: 2024,
  doi: '10.1234/jss.2024.001',
  abstract: 'This randomized controlled trial investigated the effects of 8-week high-intensity interval training (HIIT) protocol on maximal oxygen uptake (VO2 max) in elite soccer players. Twenty-four professional soccer players were randomly assigned to either HIIT group (n=12) or control group (n=12). The HIIT group performed 4×4-minute intervals at 90-95% of maximal heart rate, three times per week. Results showed significant improvements in VO2 max (p<0.001) in the HIIT group compared to controls.',
  qualityScore: 87,
  sampleSize: 24,
  studyType: 'Randomized Controlled Trial',
  keywords: ['HIIT', 'VO2 max', 'soccer', 'elite athletes', 'cardiovascular fitness'],
  citationCount: 45,
  isOpenAccess: true,
  url: 'https://example.com/paper/1'
};

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simple Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  STUDYSCORE<span className="text-primary">.INFO</span>
                </h1>
                <p className="text-xs text-gray-500 hidden sm:block">
                  Evidence-Based Sports Science Research
                </p>
              </div>
            </div>
            <nav className="hidden md:flex items-center gap-6">
              <a href="#" className="text-gray-600 hover:text-primary">Research</a>
              <a href="#" className="text-gray-600 hover:text-primary">Quality System</a>
              <a href="#" className="text-gray-600 hover:text-primary">Tools</a>
              <a href="#" className="text-gray-600 hover:text-primary">About</a>
              <Button size="sm">Get Started</Button>
            </nav>

            {/* Mobile menu button */}
            <button className="md:hidden p-2 text-gray-600 hover:text-primary">
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </header>

      <main>
        {/* Hero Section */}
        <section className="bg-white py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Evidence-Based <span className="text-primary">Sports Science</span>
              <br className="hidden sm:block" />
              <span className="block sm:inline">Research at Your Fingertips</span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 mb-8 max-w-3xl mx-auto px-4 sm:px-0">
              Search through 47,000+ peer-reviewed sports science papers with intelligent quality scoring
              and discover research relationships through our knowledge graph.
            </p>

            {/* Simple Search Bar */}
            <div className="max-w-4xl mx-auto mb-8">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search research papers..."
                  className="w-full h-12 sm:h-14 pl-12 pr-20 sm:pr-32 text-base sm:text-lg rounded-xl border-2 border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 shadow-lg"
                />
                <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 sm:h-10 px-3 sm:px-6 text-sm sm:text-base">
                  Search
                </Button>
              </div>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
              <span>✓ Research Ranked by Scientific Rigor</span>
              <span>✓ Quality Scoring System</span>
              <span>✓ Citation Export</span>
              <span>✓ Knowledge Graph Exploration</span>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Research Stats Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Research Database</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-3">
                        <FileText className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-2xl font-bold text-gray-900 mb-1">47,234</div>
                      <div className="text-sm text-gray-600">Total Papers</div>
                      <Badge variant="outline" className="text-xs mt-1">+12% vs last month</Badge>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 rounded-lg bg-green-50 flex items-center justify-center mx-auto mb-3">
                        <Award className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="text-2xl font-bold text-gray-900 mb-1">8,456</div>
                      <div className="text-sm text-gray-600">Tier 1 Studies</div>
                      <Badge variant="outline" className="text-xs mt-1">+8% high quality</Badge>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center mx-auto mb-3">
                        <Users className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="text-2xl font-bold text-gray-900 mb-1">12,890</div>
                      <div className="text-sm text-gray-600">Active Researchers</div>
                      <Badge variant="outline" className="text-xs mt-1">+15% this month</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quality Stats Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quality Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-center">
                      <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-2">
                        <BarChart3 className="h-4 w-4 text-primary" />
                      </div>
                      <div className="text-lg font-bold text-gray-900">78.5</div>
                      <div className="text-xs text-gray-500">Avg Quality Score</div>
                      <Badge variant="outline" className="text-xs mt-1">+3%</Badge>
                    </div>
                    <div className="text-center">
                      <div className="w-8 h-8 rounded-lg bg-green-50 flex items-center justify-center mx-auto mb-2">
                        <Award className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="text-lg font-bold text-gray-900">94%</div>
                      <div className="text-xs text-gray-500">Peer Reviewed</div>
                      <Badge variant="outline" className="text-xs mt-1">+1%</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Sample Research Card */}
        <section className="py-12 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Featured Research
            </h2>
            <Card className="border-l-4 border-l-green-500">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold leading-tight text-gray-900 mb-2">
                      {samplePaper.title}
                    </h3>
                    <div className="flex flex-wrap items-center gap-2 text-sm text-gray-600 mb-3">
                      <span className="font-medium">Smith, J., Johnson, M., Williams, K.</span>
                      <span>•</span>
                      <span>2024</span>
                      <span>•</span>
                      <span className="font-medium text-primary">Journal of Sports Science</span>
                    </div>
                    <div className="flex flex-wrap items-center gap-2 mb-3">
                      <Badge className="bg-green-100 text-green-800">Tier 1 - 87</Badge>
                      <Badge variant="outline" className="text-xs">Randomized Controlled Trial</Badge>
                      <Badge variant="outline" className="text-xs">n=24</Badge>
                      <Badge variant="outline" className="text-xs">45 citations</Badge>
                      <Badge className="text-xs bg-green-100 text-green-800">Open Access</Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-gray-700 leading-relaxed mb-4">
                  {samplePaper.abstract.substring(0, 300)}...
                </p>
                <div className="flex flex-wrap gap-1 mb-4">
                  {samplePaper.keywords.map((keyword, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {keyword}
                    </Badge>
                  ))}
                </div>
                <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                  <span className="text-xs text-gray-500">DOI: {samplePaper.doi}</span>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" className="text-xs">
                      Cite
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs">
                      Full Text
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-primary">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Explore Sports Science Research?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of researchers, coaches, and practitioners using StudyScore
              to make evidence-based decisions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary">
                Start Searching
              </Button>
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-primary">
                Learn More
              </Button>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
