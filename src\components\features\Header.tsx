'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { BookOpen, Menu, Search, Users, Award, FileText, BarChart3, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface HeaderProps {
  onMenuClick?: () => void;
}

const Header = ({ onMenuClick }: HeaderProps) => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const headerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (headerRef.current && !headerRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  return (
    <header ref={headerRef} className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and brand */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  STUDYSCORE<span className="text-primary">.INFO</span>
                </h1>
                <p className="text-xs text-gray-500 hidden sm:block">
                  Evidence-Based Sports Science Research
                </p>
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-6">
            {/* Research Dropdown */}
            <div className="relative">
              <button
                className="flex items-center gap-1 text-gray-600 hover:text-primary px-3 py-2 text-sm font-medium"
                onClick={() => setActiveDropdown(activeDropdown === 'research' ? null : 'research')}
              >
                Research
                <ChevronDown className="h-4 w-4" />
              </button>
              {activeDropdown === 'research' && (
                <div className="absolute top-full left-0 mt-1 w-96 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-4 grid grid-cols-2 gap-4">
                    <Link href="/search" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="flex items-center gap-2 mb-2">
                        <Search className="h-4 w-4 text-primary" />
                        <span className="font-medium">Advanced Search</span>
                      </div>
                      <p className="text-sm text-gray-600">Search 47,000+ papers with quality scoring</p>
                    </Link>
                    <Link href="/browse" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-primary" />
                        <span className="font-medium">Browse by Category</span>
                      </div>
                      <p className="text-sm text-gray-600">Explore by sport or intervention</p>
                    </Link>
                    <Link href="/knowledge-graph" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="flex items-center gap-2 mb-2">
                        <BarChart3 className="h-4 w-4 text-primary" />
                        <span className="font-medium">Knowledge Graph</span>
                      </div>
                      <p className="text-sm text-gray-600">Interactive research relationships</p>
                    </Link>
                    <Link href="/trending" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="flex items-center gap-2 mb-2">
                        <Award className="h-4 w-4 text-primary" />
                        <span className="font-medium">Trending Research</span>
                      </div>
                      <p className="text-sm text-gray-600">Most cited papers this month</p>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Quality System Dropdown */}
            <div className="relative">
              <button
                className="flex items-center gap-1 text-gray-600 hover:text-primary px-3 py-2 text-sm font-medium"
                onClick={() => setActiveDropdown(activeDropdown === 'quality' ? null : 'quality')}
              >
                Quality System
                <ChevronDown className="h-4 w-4" />
              </button>
              {activeDropdown === 'quality' && (
                <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-4 space-y-2">
                    <Link href="/quality-scoring" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="font-medium mb-1">Quality Scoring</div>
                      <p className="text-sm text-gray-600">How we rank research using Tier 1 and Tier 2 criteria</p>
                    </Link>
                    <Link href="/methodology" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="font-medium mb-1">Methodology</div>
                      <p className="text-sm text-gray-600">Academic standards and scoring transparency</p>
                    </Link>
                    <Link href="/validation" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="font-medium mb-1">Expert Validation</div>
                      <p className="text-sm text-gray-600">Academic advisory board and peer review process</p>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Tools & Resources Dropdown */}
            <div className="relative">
              <button
                className="flex items-center gap-1 text-gray-600 hover:text-primary px-3 py-2 text-sm font-medium"
                onClick={() => setActiveDropdown(activeDropdown === 'tools' ? null : 'tools')}
              >
                Tools & Resources
                <ChevronDown className="h-4 w-4" />
              </button>
              {activeDropdown === 'tools' && (
                <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-4 space-y-2">
                    <Link href="/citation-generator" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="font-medium mb-1">Citation Generator</div>
                      <p className="text-sm text-gray-600">APA, MLA, Vancouver format exports</p>
                    </Link>
                    <Link href="/evidence-synthesis" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="font-medium mb-1">Evidence Synthesis</div>
                      <p className="text-sm text-gray-600">AI-powered analysis across studies</p>
                    </Link>
                    <Link href="/research-collections" className="block p-3 rounded-md hover:bg-gray-50">
                      <div className="font-medium mb-1">Research Collections</div>
                      <p className="text-sm text-gray-600">Save and organize studies</p>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* About Link */}
            <Link href="/about" className="text-gray-600 hover:text-primary px-3 py-2 text-sm font-medium">
              About
            </Link>

            <div className="flex items-center gap-3 ml-4 border-l border-gray-200 pl-4">
              <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
                <Users className="h-4 w-4 mr-2" />
                For Researchers
              </Button>
              <Button variant="outline" size="sm">
                Sign In
              </Button>
              <Button size="sm" className="bg-primary hover:bg-primary/90">
                Get Started
              </Button>
            </div>
          </nav>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={onMenuClick}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </header>
  );
};



export default Header;
