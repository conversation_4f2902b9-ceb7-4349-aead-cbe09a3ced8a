'use client';

import { Badge } from '@/components/ui/badge';
import { Award, Star, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';

export type QualityLevel = 'tier1' | 'tier2' | 'moderate' | 'low' | 'insufficient';

interface QualityBadgeProps {
  level: QualityLevel;
  score?: number;
  showTooltip?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const qualityConfig = {
  tier1: {
    label: 'Tier 1',
    description: 'Highest quality evidence - Systematic reviews, meta-analyses, and high-quality RCTs',
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: Award,
    scoreRange: '90-100'
  },
  tier2: {
    label: 'Tier 2', 
    description: 'High quality evidence - Well-designed RCTs and controlled studies',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: Star,
    scoreRange: '75-89'
  },
  moderate: {
    label: 'Moderate',
    description: 'Moderate quality evidence - Observational studies with good methodology',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: CheckCircle,
    scoreRange: '60-74'
  },
  low: {
    label: 'Low',
    description: 'Lower quality evidence - Case studies, small samples, or methodological limitations',
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: AlertCircle,
    scoreRange: '40-59'
  },
  insufficient: {
    label: 'Insufficient',
    description: 'Insufficient evidence - Preliminary studies or significant methodological concerns',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: Info,
    scoreRange: '0-39'
  }
};

const QualityBadge = ({
  level,
  score,
  showTooltip = true,
  size = 'md',
  className
}: QualityBadgeProps) => {
  const config = qualityConfig[level];
  const Icon = config.icon;
  const [showDetails, setShowDetails] = useState(false);

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const badge = (
    <div className="relative">
      <Badge
        variant="outline"
        className={cn(
          config.color,
          sizeClasses[size],
          'font-medium border flex items-center gap-1.5 cursor-pointer',
          className
        )}
        onMouseEnter={() => showTooltip && setShowDetails(true)}
        onMouseLeave={() => setShowDetails(false)}
      >
        <Icon className={iconSizes[size]} />
        {config.label}
        {score && (
          <span className="ml-1 font-bold">
            {score}
          </span>
        )}
      </Badge>

      {showTooltip && showDetails && (
        <div className="absolute bottom-full left-0 mb-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-50">
          <div className="space-y-2">
            <div className="font-semibold flex items-center gap-2">
              <Icon className="h-4 w-4" />
              {config.label} Quality
              {score && <span>({score}/100)</span>}
            </div>
            <p>{config.description}</p>
            <div className="text-gray-300">
              Score range: {config.scoreRange}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return badge;
};

// Helper function to determine quality level from score
export const getQualityLevel = (score: number): QualityLevel => {
  if (score >= 90) return 'tier1';
  if (score >= 75) return 'tier2';
  if (score >= 60) return 'moderate';
  if (score >= 40) return 'low';
  return 'insufficient';
};

// Component for displaying multiple quality indicators
interface QualityIndicatorsProps {
  overallScore: number;
  methodologyScore?: number;
  sampleSizeScore?: number;
  designScore?: number;
  className?: string;
}

export const QualityIndicators = ({
  overallScore,
  methodologyScore,
  sampleSizeScore,
  designScore,
  className
}: QualityIndicatorsProps) => {
  const overallLevel = getQualityLevel(overallScore);

  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      <QualityBadge level={overallLevel} score={overallScore} />
      
      {methodologyScore && (
        <Badge variant="outline" className="text-xs">
          Methodology: {methodologyScore}
        </Badge>
      )}
      
      {sampleSizeScore && (
        <Badge variant="outline" className="text-xs">
          Sample: {sampleSizeScore}
        </Badge>
      )}
      
      {designScore && (
        <Badge variant="outline" className="text-xs">
          Design: {designScore}
        </Badge>
      )}
    </div>
  );
};

export default QualityBadge;
