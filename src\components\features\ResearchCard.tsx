'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import QualityBadge, { getQualityLevel } from './QualityBadge';
import { 
  ExternalLink, 
  BookmarkPlus, 
  Quote, 
  Users, 
  Calendar,
  TrendingUp,
  FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Author {
  name: string;
  affiliation?: string;
}

interface ResearchPaper {
  id: string;
  title: string;
  authors: Author[];
  journal: string;
  year: number;
  doi?: string;
  abstract: string;
  qualityScore: number;
  sampleSize?: number;
  studyType: string;
  keywords: string[];
  citationCount?: number;
  isOpenAccess?: boolean;
  url?: string;
}

interface ResearchCardProps {
  paper: ResearchPaper;
  onBookmark?: (paperId: string) => void;
  onCite?: (paperId: string) => void;
  className?: string;
  showFullAbstract?: boolean;
}

const ResearchCard = ({ 
  paper, 
  onBookmark, 
  onCite, 
  className,
  showFullAbstract = false 
}: ResearchCardProps) => {
  const qualityLevel = getQualityLevel(paper.qualityScore);
  
  const truncatedAbstract = showFullAbstract 
    ? paper.abstract 
    : paper.abstract.length > 300 
      ? `${paper.abstract.substring(0, 300)}...` 
      : paper.abstract;

  const handleExternalLink = () => {
    if (paper.url) {
      window.open(paper.url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <Card className={cn(
      "hover:shadow-lg transition-all duration-200 border-l-4",
      qualityLevel === 'tier1' && "border-l-green-500",
      qualityLevel === 'tier2' && "border-l-blue-500", 
      qualityLevel === 'moderate' && "border-l-yellow-500",
      qualityLevel === 'low' && "border-l-orange-500",
      qualityLevel === 'insufficient' && "border-l-gray-400",
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold leading-tight text-gray-900 mb-2 line-clamp-2">
              {paper.title}
            </h3>
            
            <div className="flex flex-wrap items-center gap-2 text-sm text-gray-600 mb-3">
              <span className="font-medium">
                {paper.authors.slice(0, 3).map(author => author.name).join(', ')}
                {paper.authors.length > 3 && ` +${paper.authors.length - 3} more`}
              </span>
              <span>•</span>
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {paper.year}
              </span>
              <span>•</span>
              <span className="font-medium text-primary">{paper.journal}</span>
            </div>

            <div className="flex flex-wrap items-center gap-2 mb-3">
              <QualityBadge level={qualityLevel} score={paper.qualityScore} size="sm" />
              
              <Badge variant="outline" className="text-xs">
                {paper.studyType}
              </Badge>
              
              {paper.sampleSize && (
                <Badge variant="outline" className="text-xs flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  n={paper.sampleSize}
                </Badge>
              )}
              
              {paper.citationCount && (
                <Badge variant="outline" className="text-xs flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  {paper.citationCount} citations
                </Badge>
              )}
              
              {paper.isOpenAccess && (
                <Badge className="text-xs bg-green-100 text-green-800">
                  Open Access
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex flex-col gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onBookmark?.(paper.id)}
              className="text-gray-500 hover:text-primary"
            >
              <BookmarkPlus className="h-4 w-4" />
            </Button>
            
            {paper.url && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleExternalLink}
                className="text-gray-500 hover:text-primary"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          <div>
            <p className="text-sm text-gray-700 leading-relaxed">
              {truncatedAbstract}
            </p>
            {!showFullAbstract && paper.abstract.length > 300 && (
              <button className="text-primary text-sm hover:underline mt-1">
                Read more
              </button>
            )}
          </div>

          {paper.keywords.length > 0 && (
            <div>
              <div className="flex flex-wrap gap-1">
                {paper.keywords.slice(0, 6).map((keyword, index) => (
                  <Badge 
                    key={index} 
                    variant="secondary" 
                    className="text-xs cursor-pointer hover:bg-primary hover:text-white transition-colors"
                  >
                    {keyword}
                  </Badge>
                ))}
                {paper.keywords.length > 6 && (
                  <Badge variant="secondary" className="text-xs">
                    +{paper.keywords.length - 6} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <div className="flex items-center gap-4 text-xs text-gray-500">
              {paper.doi && (
                <span>DOI: {paper.doi}</span>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCite?.(paper.id)}
                className="text-xs"
              >
                <Quote className="h-3 w-3 mr-1" />
                Cite
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                className="text-xs"
              >
                <FileText className="h-3 w-3 mr-1" />
                Full Text
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ResearchCard;
