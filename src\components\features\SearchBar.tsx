'use client';

import { useState } from 'react';
import { Search, Filter, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface SearchBarProps {
  onSearch?: (query: string) => void;
  onFilterClick?: () => void;
  placeholder?: string;
  className?: string;
  showSuggestions?: boolean;
}

const SearchBar = ({ 
  onSearch, 
  onFilterClick, 
  placeholder = "Search sports science research...", 
  className,
  showSuggestions = true 
}: SearchBarProps) => {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim() && onSearch) {
      onSearch(query.trim());
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const popularSearches = [
    'ACL injury prevention',
    'Protein synthesis muscle',
    'VO2 max training',
    'Concussion recovery',
    'Strength training adaptations'
  ];

  const recentTrends = [
    'Heat stress performance',
    'Sleep recovery athletes',
    'Nutrition periodization'
  ];

  return (
    <div className={cn("relative w-full max-w-4xl mx-auto", className)}>
      {/* Main Search Bar */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative flex items-center">
          <div className="absolute left-4 z-10">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          
          <Input
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setTimeout(() => setIsFocused(false), 200)}
            placeholder={placeholder}
            className="h-14 pl-12 pr-32 text-lg rounded-xl border-2 border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 shadow-lg"
          />
          
          <div className="absolute right-2 flex items-center gap-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onFilterClick}
              className="text-gray-500 hover:text-primary"
            >
              <Filter className="h-4 w-4" />
            </Button>
            
            <Button 
              type="submit" 
              size="sm"
              className="bg-primary hover:bg-primary/90 text-white px-6"
            >
              Search
            </Button>
          </div>
        </div>
      </form>

      {/* Search Suggestions Dropdown */}
      {isFocused && showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto">
          <div className="p-4">
            {query.length > 0 ? (
              // Show search suggestions based on query
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Suggestions</h4>
                <div className="space-y-2">
                  {popularSearches
                    .filter(search => search.toLowerCase().includes(query.toLowerCase()))
                    .slice(0, 5)
                    .map((suggestion, index) => (
                      <button
                        key={index}
                        className="w-full text-left p-2 hover:bg-gray-50 rounded-md text-sm"
                        onClick={() => {
                          setQuery(suggestion);
                          if (onSearch) onSearch(suggestion);
                        }}
                      >
                        <Search className="h-4 w-4 inline mr-2 text-gray-400" />
                        {suggestion}
                      </button>
                    ))}
                </div>
              </div>
            ) : (
              // Show popular searches and trends when no query
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Trending Research
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {recentTrends.map((trend, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="cursor-pointer hover:bg-primary hover:text-white transition-colors"
                        onClick={() => {
                          setQuery(trend);
                          if (onSearch) onSearch(trend);
                        }}
                      >
                        {trend}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Popular Searches</h4>
                  <div className="space-y-1">
                    {popularSearches.slice(0, 5).map((search, index) => (
                      <button
                        key={index}
                        className="w-full text-left p-2 hover:bg-gray-50 rounded-md text-sm text-gray-600"
                        onClick={() => {
                          setQuery(search);
                          if (onSearch) onSearch(search);
                        }}
                      >
                        <Search className="h-4 w-4 inline mr-2 text-gray-400" />
                        {search}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="pt-3 border-t border-gray-100">
                  <p className="text-xs text-gray-500">
                    Search through <span className="font-medium">47,000+</span> peer-reviewed sports science papers
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
