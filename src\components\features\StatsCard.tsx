'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  FileText,
  Award,
  Users,
  Calendar,
  BarChart3,
  Target,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatItem {
  label: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon?: React.ComponentType<{ className?: string }>;
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
}

interface StatsCardProps {
  title: string;
  stats: StatItem[];
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
}

const StatsCard = ({ title, stats, className, variant = 'default' }: StatsCardProps) => {
  const getTrendIcon = (change?: number) => {
    if (!change) return Minus;
    if (change > 0) return TrendingUp;
    if (change < 0) return TrendingDown;
    return Minus;
  };

  const getTrendColor = (change?: number) => {
    if (!change) return 'text-gray-400';
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-400';
  };

  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'success':
        return 'text-green-600 bg-green-50';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50';
      case 'danger':
        return 'text-red-600 bg-red-50';
      case 'info':
        return 'text-blue-600 bg-blue-50';
      case 'primary':
        return 'text-primary bg-primary/10';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (variant === 'compact') {
    return (
      <Card className={cn("", className)}>
        <CardContent className="p-4">
          <h4 className="font-medium text-gray-900 mb-3">{title}</h4>
          <div className="grid grid-cols-2 gap-3">
            {stats.map((stat, index) => {
              const Icon = stat.icon || BarChart3;
              const TrendIcon = getTrendIcon(stat.change);
              
              return (
                <div key={index} className="text-center">
                  <div className={cn(
                    "w-8 h-8 rounded-lg flex items-center justify-center mx-auto mb-2",
                    getColorClasses(stat.color)
                  )}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div className="text-lg font-bold text-gray-900">{stat.value}</div>
                  <div className="text-xs text-gray-500">{stat.label}</div>
                  {stat.change !== undefined && (
                    <div className={cn("flex items-center justify-center gap-1 text-xs mt-1", getTrendColor(stat.change))}>
                      <TrendIcon className="h-3 w-3" />
                      {Math.abs(stat.change)}%
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon || BarChart3;
            const TrendIcon = getTrendIcon(stat.change);
            
            return (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-10 h-10 rounded-lg flex items-center justify-center",
                    getColorClasses(stat.color)
                  )}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{stat.label}</div>
                    {stat.changeLabel && (
                      <div className="text-sm text-gray-500">{stat.changeLabel}</div>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-xl font-bold text-gray-900">{stat.value}</div>
                  {stat.change !== undefined && (
                    <div className={cn("flex items-center gap-1 text-sm", getTrendColor(stat.change))}>
                      <TrendIcon className="h-4 w-4" />
                      {Math.abs(stat.change)}%
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon || BarChart3;
            const TrendIcon = getTrendIcon(stat.change);
            
            return (
              <div key={index} className="text-center p-4 border border-gray-200 rounded-lg">
                <div className={cn(
                  "w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3",
                  getColorClasses(stat.color)
                )}>
                  <Icon className="h-6 w-6" />
                </div>
                
                <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                <div className="text-sm text-gray-600 mb-2">{stat.label}</div>
                
                {stat.change !== undefined && (
                  <div className="flex items-center justify-center gap-1">
                    <Badge 
                      variant="outline" 
                      className={cn("text-xs", getTrendColor(stat.change))}
                    >
                      <TrendIcon className="h-3 w-3 mr-1" />
                      {Math.abs(stat.change)}%
                    </Badge>
                  </div>
                )}
                
                {stat.changeLabel && (
                  <div className="text-xs text-gray-500 mt-1">{stat.changeLabel}</div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// Pre-configured stats cards for common use cases
export const ResearchStatsCard = ({ className }: { className?: string }) => {
  const stats: StatItem[] = [
    {
      label: 'Total Papers',
      value: '47,234',
      change: 12,
      changeLabel: 'vs last month',
      icon: FileText,
      color: 'primary'
    },
    {
      label: 'Tier 1 Studies',
      value: '8,456',
      change: 8,
      changeLabel: 'high quality',
      icon: Award,
      color: 'success'
    },
    {
      label: 'Active Researchers',
      value: '12,890',
      change: 15,
      changeLabel: 'this month',
      icon: Users,
      color: 'info'
    }
  ];

  return <StatsCard title="Research Database" stats={stats} className={className} />;
};

export const QualityStatsCard = ({ className }: { className?: string }) => {
  const stats: StatItem[] = [
    {
      label: 'Avg Quality Score',
      value: '78.5',
      change: 3,
      icon: Target,
      color: 'primary'
    },
    {
      label: 'Peer Reviewed',
      value: '94%',
      change: 1,
      icon: Award,
      color: 'success'
    },
    {
      label: 'Recent Updates',
      value: '1,234',
      change: 22,
      changeLabel: 'this week',
      icon: Zap,
      color: 'warning'
    }
  ];

  return <StatsCard title="Quality Metrics" stats={stats} className={className} variant="compact" />;
};

export default StatsCard;
